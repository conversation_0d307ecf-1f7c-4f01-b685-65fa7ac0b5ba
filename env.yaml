name: 4dvargan
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch
  - nvidia
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - bioconda
  - conda-forge
  - defaults
dependencies:
  - _anaconda_depends=2022.05=py38_0
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - absl-py=1.3.0=pyhd8ed1ab_0
  - aiohttp=3.8.3=py38h5eee18b_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - alabaster=0.7.12=pyhd3eb1b0_0
  - anaconda=custom=py38_1
  - anaconda-client=1.11.0=py38h06a4308_0
  - anaconda-project=0.11.1=py38h06a4308_0
  - antlr-python-runtime=4.9.3=pyhd8ed1ab_1
  - anyio=3.5.0=py38h06a4308_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py38h7f8727e_0
  - arrow=1.2.3=py38h06a4308_0
  - astroid=2.11.7=py38h06a4308_0
  - astropy=5.1=py38h7deecbd_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - async-timeout=4.0.2=py38h06a4308_0
  - atomicwrites=1.4.0=py_0
  - attrs=22.1.0=py38h06a4308_0
  - automat=20.2.0=py_0
  - autopep8=1.6.0=pyhd3eb1b0_1
  - babel=2.9.1=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - backports=1.1=pyhd3eb1b0_0
  - backports.functools_lru_cache=1.6.4=pyhd3eb1b0_0
  - backports.tempfile=1.0=pyhd3eb1b0_1
  - backports.weakref=1.0.post1=py_1
  - bcrypt=3.2.0=py38h5eee18b_1
  - beautifulsoup4=4.11.1=py38h06a4308_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - bitarray=2.5.1=py38h5eee18b_0
  - bkcharts=0.2=py38h06a4308_1
  - black=22.6.0=py38h06a4308_0
  - blas=1.0=mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - blinker=1.5=pyhd8ed1ab_0
  - blosc=1.21.0=h4ff587b_1
  - bokeh=2.4.3=py38h06a4308_0
  - boto3=1.24.28=py38h06a4308_0
  - botocore=1.27.59=py38h06a4308_0
  - bottleneck=1.3.5=py38h7deecbd_0
  - brotli=1.0.9=h5eee18b_7
  - brotli-bin=1.0.9=h5eee18b_7
  - brotlipy=0.7.0=py38h27cfd23_1003
  - brunsli=0.1=h2531618_0
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.18.1=h7f8727e_0
  - ca-certificates=2023.7.22=hbcca054_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - cartopy=0.18.0=py38h0d9ca2b_1
  - certifi=2023.7.22=pyhd8ed1ab_0
  - cffi=1.15.1=py38h5eee18b_2
  - cfgv=3.3.1=pyhd8ed1ab_0
  - cfitsio=3.470=hf0d0db6_6
  - cftime=1.6.0=py38h71d37f0_1
  - chardet=4.0.0=py38h06a4308_1003
  - charls=2.2.0=h2531618_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py38h06a4308_0
  - cloudpickle=2.0.0=pyhd3eb1b0_0
  - clyent=1.2.2=py38_1
  - colorama=0.4.5=py38h06a4308_0
  - colorcet=3.0.1=py38h06a4308_0
  - commonmark=0.9.1=py_0
  - conda=22.11.0=py38h06a4308_1
  - conda-content-trust=0.1.3=py38h06a4308_0
  - conda-pack=0.6.0=pyhd3eb1b0_0
  - conda-package-handling=1.9.0=py38h5eee18b_1
  - conda-token=0.4.0=pyhd3eb1b0_0
  - constantly=15.1.0=pyh2b92418_0
  - cookiecutter=1.7.3=pyhd3eb1b0_0
  - cryptography=38.0.1=py38h9ce1e76_0
  - cssselect=1.1.0=pyhd3eb1b0_0
  - cuda=11.6.2=0
  - cuda-cccl=11.6.55=hf6102b2_0
  - cuda-command-line-tools=11.6.2=0
  - cuda-compiler=11.6.2=0
  - cuda-cudart=11.6.55=he381448_0
  - cuda-cudart-dev=11.6.55=h42ad0f4_0
  - cuda-cuobjdump=11.6.124=h2eeebcb_0
  - cuda-cupti=11.6.124=h86345e5_0
  - cuda-cuxxfilt=11.6.124=hecbf4f6_0
  - cuda-driver-dev=11.6.55=0
  - cuda-gdb=11.8.86=0
  - cuda-libraries=11.6.2=0
  - cuda-libraries-dev=11.6.2=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=11.8.86=0
  - cuda-nsight-compute=11.8.0=0
  - cuda-nvcc=11.6.124=hbba6d2d_0
  - cuda-nvdisasm=11.8.86=0
  - cuda-nvml-dev=11.6.55=haa9ef22_0
  - cuda-nvprof=11.8.87=0
  - cuda-nvprune=11.6.124=he22ec0a_0
  - cuda-nvrtc=11.6.124=h020bade_0
  - cuda-nvrtc-dev=11.6.124=h249d397_0
  - cuda-nvtx=11.6.124=h0630a44_0
  - cuda-nvvp=11.8.87=0
  - cuda-runtime=11.6.2=0
  - cuda-samples=11.6.101=h8efea70_0
  - cuda-sanitizer-api=11.8.86=0
  - cuda-toolkit=11.6.2=0
  - cuda-tools=11.6.2=0
  - cuda-visual-tools=11.6.2=0
  - curl=7.88.1=hdc1c0ab_1
  - cycler=0.11.0=pyhd3eb1b0_0
  - cython=0.29.32=py38h6a678d5_0
  - cytoolz=0.12.0=py38h5eee18b_0
  - daal4py=2021.6.0=py38h79cecc1_1
  - dal=2021.6.0=hdb19cb5_916
  - dask=2022.2.1=pyhd3eb1b0_0
  - dask-core=2022.2.1=pyhd3eb1b0_0
  - dataclasses=0.8=pyh6d0b6a4_7
  - datashader=0.14.2=py38h06a4308_0
  - datashape=0.5.4=py38h06a4308_1
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.5.1=py38h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.6=py38h06a4308_0
  - distlib=0.3.6=pyhd8ed1ab_0
  - distributed=2022.2.1=pyhd3eb1b0_0
  - docutils=0.18.1=py38h06a4308_3
  - einops=0.6.0=pyhd8ed1ab_0
  - entrypoints=0.4=py38h06a4308_0
  - et_xmlfile=1.1.0=py38h06a4308_0
  - exceptiongroup=1.0.4=pyhd8ed1ab_0
  - executing=0.8.3=pyhd3eb1b0_0
  - expat=2.4.9=h6a678d5_0
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.6.0=pyhd3eb1b0_0
  - flake8=4.0.1=pyhd3eb1b0_1
  - flask=2.1.3=py38h06a4308_0
  - flit-core=3.6.0=pyhd3eb1b0_0
  - fontconfig=2.14.1=hef1e5e3_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - frozenlist=1.3.3=py38h5eee18b_0
  - fsspec=2022.10.0=py38h06a4308_0
  - future=0.18.2=py38_1
  - gds-tools=1.4.0.31=0
  - gensim=4.3.0=py38h6a678d5_0
  - geos=3.8.0=he6710b0_0
  - giflib=5.2.1=h7b6447c_0
  - glib=2.69.1=he621ea3_2
  - glob2=0.7=pyhd3eb1b0_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py38heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - google-api-core=2.10.1=py38h06a4308_0
  - google-auth=2.6.0=pyhd3eb1b0_0
  - google-auth-oauthlib=0.4.6=pyhd8ed1ab_0
  - google-cloud-core=2.3.2=py38h06a4308_0
  - google-cloud-storage=2.6.0=py38h06a4308_0
  - google-crc32c=1.5.0=py38h5eee18b_0
  - google-resumable-media=2.4.0=py38h06a4308_0
  - googleapis-common-protos=1.56.4=py38h06a4308_0
  - greenlet=1.1.3=py38h6a678d5_0
  - grpc-cpp=1.48.1=hc2bec63_1
  - grpcio=1.48.1=py38hf8115fd_1
  - gst-plugins-base=1.14.0=h8213a91_2
  - gstreamer=1.14.0=h28cd5cc_2
  - h5py=2.10.0=py38h7918eee_0
  - hdf4=4.2.13=h10796ff_1005
  - hdf5=1.10.4=hb1b8bf9_0
  - heapdict=1.0.1=pyhd3eb1b0_0
  - holoviews=1.15.2=py38h06a4308_0
  - huggingface_hub=0.11.1=pyhd8ed1ab_0
  - hvplot=0.8.1=py38h06a4308_0
  - hydra-core=1.2.0=pyhd8ed1ab_0
  - hyperlink=21.0.0=pyhd3eb1b0_0
  - icu=58.2=he6710b0_3
  - identify=2.5.9=pyhd8ed1ab_0
  - idna=3.4=py38h06a4308_0
  - imagecodecs=2021.8.26=py38hf0132c2_1
  - imageio=2.19.3=py38h06a4308_0
  - imagesize=1.4.1=py38h06a4308_0
  - importlib-metadata=4.11.3=py38h06a4308_0
  - importlib_metadata=4.11.3=hd3eb1b0_0
  - importlib_resources=5.2.0=pyhd3eb1b0_1
  - incremental=21.3.0=pyhd3eb1b0_0
  - inflection=0.5.1=py38h06a4308_0
  - iniconfig=1.1.1=pyhd3eb1b0_0
  - intake=0.6.6=py38h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.15.2=py38h06a4308_0
  - ipython=7.31.1=py38h06a4308_1
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - ipywidgets=7.6.5=pyhd3eb1b0_1
  - isort=5.9.3=pyhd3eb1b0_0
  - itemadapter=0.3.0=pyhd3eb1b0_0
  - itemloaders=1.0.4=pyhd3eb1b0_1
  - itsdangerous=2.0.1=pyhd3eb1b0_0
  - jdcal=1.4.1=pyhd3eb1b0_0
  - jedi=0.18.1=py38h06a4308_1
  - jeepney=0.7.1=pyhd3eb1b0_0
  - jellyfish=0.9.0=py38h7f8727e_0
  - jinja2=3.1.2=py38h06a4308_0
  - jinja2-time=0.2.0=pyhd3eb1b0_3
  - jmespath=0.10.0=pyhd3eb1b0_0
  - joblib=1.1.1=py38h06a4308_0
  - jpeg=9e=h7f8727e_0
  - jq=1.6=h27cfd23_1000
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.16.0=py38h06a4308_0
  - jupyter=1.0.0=py38h06a4308_8
  - jupyter_client=7.4.7=py38h06a4308_0
  - jupyter_console=6.4.3=pyhd3eb1b0_0
  - jupyter_core=4.11.2=py38h06a4308_0
  - jupyter_server=1.18.1=py38h06a4308_0
  - jupyterlab=3.5.0=py38h06a4308_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.16.3=py38h06a4308_0
  - jupyterlab_widgets=1.0.0=pyhd3eb1b0_1
  - jxrlib=1.1=h7b6447c_2
  - keyring=23.4.0=py38h06a4308_0
  - keyutils=1.6.1=h166bdaf_0
  - kiwisolver=1.4.2=py38h295c915_0
  - krb5=1.20.1=h81ceb04_0
  - lame=3.100=h7b6447c_0
  - lazy-object-proxy=1.6.0=py38h27cfd23_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libabseil=20220623.0=cxx17_h05df665_6
  - libaec=1.0.4=he6710b0_1
  - libarchive=3.5.2=hada088e_3
  - libblas=3.9.0=12_linux64_mkl
  - libbrotlicommon=1.0.9=h5eee18b_7
  - libbrotlidec=1.0.9=h5eee18b_7
  - libbrotlienc=1.0.9=h5eee18b_7
  - libcblas=3.9.0=12_linux64_mkl
  - libclang=10.0.1=default_hb85057a_2
  - libcrc32c=1.1.2=h6a678d5_0
  - libcublas=11.11.3.6=0
  - libcublas-dev=11.11.3.6=0
  - libcufft=10.9.0.58=0
  - libcufft-dev=10.9.0.58=0
  - libcufile=1.4.0.31=0
  - libcufile-dev=1.4.0.31=0
  - libcurand=10.3.0.86=0
  - libcurand-dev=10.3.0.86=0
  - libcurl=7.88.1=hdc1c0ab_1
  - libcusolver=11.4.1.48=0
  - libcusolver-dev=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libcusparse-dev=11.7.5.86=0
  - libdeflate=1.8=h7f8727e_5
  - libedit=3.1.20210910=h7f8727e_0
  - libev=4.33=h7f8727e_1
  - libevent=2.1.12=hf998b51_1
  - libffi=3.4.2=h6a678d5_6
  - libgcc-ng=13.1.0=he5830b7_0
  - libgfortran-ng=7.5.0=ha8ba4b0_17
  - libgfortran4=7.5.0=ha8ba4b0_17
  - libgomp=13.1.0=he5830b7_0
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - liblapack=3.9.0=12_linux64_mkl
  - liblief=0.11.5=h295c915_1
  - libllvm10=10.0.1=hbcb73fb_5
  - libllvm11=11.1.0=h9e868ea_6
  - libnetcdf=4.7.3=hb80b6cc_0
  - libnghttp2=1.52.0=h61bc06f_0
  - libnpp=11.8.0.86=0
  - libnpp-dev=11.8.0.86=0
  - libnsl=2.0.0=h7f98852_0
  - libnvjpeg=11.9.0.86=0
  - libnvjpeg-dev=11.9.0.86=0
  - libpng=1.6.37=hbc83047_0
  - libpq=12.15=hdbd6064_1
  - libprotobuf=3.20.1=h4ff587b_0
  - libsodium=1.0.18=h7b6447c_0
  - libspatialindex=1.9.3=h2531618_0
  - libsqlite=3.42.0=h2797004_0
  - libssh2=1.11.0=h0841786_0
  - libstdcxx-ng=13.1.0=hfd8a6a1_0
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.4.0=hecacb30_2
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=2.38.1=h0b41bf4_0
  - libwebp=1.2.4=h11a3e52_0
  - libwebp-base=1.2.4=h5eee18b_0
  - libxcb=1.15=h7f8727e_0
  - libxkbcommon=1.0.1=hfa300c1_0
  - libxml2=2.9.14=h74e7548_0
  - libxslt=1.1.35=h4e12654_0
  - libzlib=1.2.13=hd590300_5
  - libzopfli=1.0.3=he6710b0_0
  - llvmlite=0.38.1=py38h38d86a4_0
  - locket=1.0.0=py38h06a4308_0
  - lxml=4.9.1=py38h1edc446_0
  - lz4-c=1.9.3=h295c915_1
  - lzo=2.10=h7b6447c_2
  - markdown=3.3.4=py38h06a4308_0
  - markupsafe=2.1.1=py38h7f8727e_0
  - matplotlib=3.5.3=py38h06a4308_0
  - matplotlib-base=3.5.3=py38hf590b9c_0
  - matplotlib-inline=0.1.6=py38h06a4308_0
  - mccabe=0.7.0=pyhd3eb1b0_0
  - mistune=0.8.4=py38h7b6447c_1000
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.1=py38hd3c417c_0
  - mkl_random=1.2.2=py38h51133e4_0
  - mock=4.0.3=pyhd3eb1b0_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpi=1.0=mpich
  - mpich=3.3.2=hc856adb_0
  - mpmath=1.2.1=py38h06a4308_0
  - msgpack-python=1.0.3=py38hd09550d_0
  - multidict=6.0.2=py38h5eee18b_0
  - multipledispatch=0.6.0=py38_0
  - munkres=1.1.4=py_0
  - mypy_extensions=0.4.3=py38h06a4308_1
  - nbclassic=0.4.8=py38h06a4308_0
  - nbclient=0.5.13=py38h06a4308_0
  - nbconvert=6.5.4=py38h06a4308_0
  - nbformat=5.5.0=py38h06a4308_0
  - ncurses=6.3=h5eee18b_3
  - nest-asyncio=1.5.5=py38h06a4308_0
  - netcdf4=1.5.3=py38hbf33ddf_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=2.8.4=py38h06a4308_0
  - nltk=3.7=pyhd3eb1b0_0
  - nodeenv=1.7.0=pyhd8ed1ab_0
  - nose=1.3.7=pyhd3eb1b0_1008
  - notebook=6.5.2=py38h06a4308_0
  - notebook-shim=0.2.2=py38h06a4308_0
  - nsight-compute=2022.3.0.22=0
  - nspr=4.33=h295c915_0
  - nss=3.74=h0370c37_0
  - numba=0.55.2=py38hdc3674a_0
  - numexpr=2.8.4=py38he184ba9_0
  - numpy=1.20.3=py38h9894fe3_0
  - numpy-base=1.21.5=py38ha15fc14_3
  - numpydoc=1.5.0=py38h06a4308_0
  - oauthlib=3.2.2=pyhd8ed1ab_0
  - olefile=0.46=pyhd3eb1b0_0
  - omegaconf=2.2.3=py38h578d9bd_0
  - oniguruma=*******=h27cfd23_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h3ad879b_0
  - openpyxl=3.0.10=py38h5eee18b_0
  - openssl=3.1.1=hd590300_1
  - packaging=21.3=pyhd3eb1b0_0
  - pandas=1.5.1=py38h417a72b_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - panel=0.14.1=py38h06a4308_0
  - param=1.12.2=py38h06a4308_0
  - parsel=1.6.0=py38h06a4308_0
  - parso=0.8.3=pyhd3eb1b0_0
  - partd=1.2.0=pyhd3eb1b0_1
  - patchelf=0.15.0=h6a678d5_0
  - pathspec=0.9.0=py38h06a4308_0
  - patsy=0.5.2=py38h06a4308_1
  - pcre=8.45=h295c915_0
  - pep8=1.7.1=py38h06a4308_1
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.2.0=py38hace64e9_1
  - pip=22.2.2=py38h06a4308_0
  - pkginfo=1.8.3=py38h06a4308_0
  - pkgutil-resolve-name=1.3.10=py38h06a4308_0
  - platformdirs=2.5.2=py38h06a4308_0
  - plotly=5.9.0=py38h06a4308_0
  - pluggy=1.0.0=py38h06a4308_1
  - ply=3.11=py38_0
  - poyo=0.5.0=pyhd3eb1b0_0
  - pre-commit=2.20.0=py38h578d9bd_1
  - proj=6.2.1=hc80f0dc_0
  - prometheus_client=0.14.1=py38h06a4308_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - prompt_toolkit=3.0.20=hd3eb1b0_0
  - protego=0.1.16=py_0
  - protobuf=3.20.1=py38h295c915_0
  - psutil=5.9.0=py38h5eee18b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - py=1.11.0=pyhd3eb1b0_0
  - py-lief=0.11.5=py38h295c915_1
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycodestyle=2.8.0=pyhd3eb1b0_0
  - pycosat=0.6.4=py38h5eee18b_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyct=0.4.8=py38h06a4308_1
  - pycurl=7.45.2=py38hdbd6064_1
  - pydeprecate=0.3.2=pyhd8ed1ab_0
  - pydispatcher=2.0.5=py38h06a4308_2
  - pydocstyle=6.1.1=pyhd3eb1b0_0
  - pyerfa=*******=py38h71d37f0_2
  - pyflakes=2.4.0=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pyhamcrest=2.0.2=pyhd3eb1b0_2
  - pyjwt=2.6.0=pyhd8ed1ab_0
  - pylint=2.14.5=py38h06a4308_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pyodbc=4.0.34=py38h6a678d5_0
  - pyopenssl=23.2.0=pyhd8ed1ab_1
  - pyparsing=3.0.9=py38h06a4308_0
  - pyqt=5.15.7=py38h6a678d5_1
  - pyqt5-sip=12.11.0=py38h6a678d5_1
  - pyqtwebengine=5.15.7=py38h6a678d5_1
  - pyrsistent=0.18.0=py38heee7806_0
  - pyshp=2.3.1=pyhd8ed1ab_0
  - pysocks=1.7.1=py38h06a4308_0
  - pytables=3.6.1=py38h9fd0a39_0
  - pytest=7.2.0=pyhd8ed1ab_2
  - python=3.8.15=he550d4f_1_cpython
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py38h06a4308_0
  - python-libarchive-c=2.9=pyhd3eb1b0_1
  - python-lsp-black=1.2.1=py38h06a4308_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.5.0=py38h06a4308_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python-snappy=0.6.0=py38h2531618_3
  - python_abi=3.8=2_cp38
  - pytorch=1.13.0=py3.8_cuda11.6_cudnn8.3.2_0
  - pytorch-cuda=11.6=h867d48c_0
  - pytorch-lightning=1.7.7=pyhd8ed1ab_0
  - pytorch-mutex=1.0=cuda
  - pytz=2022.1=py38h06a4308_0
  - pyviz_comms=2.0.2=pyhd3eb1b0_0
  - pywavelets=1.3.0=py38h7f8727e_0
  - pyxdg=0.27=pyhd3eb1b0_0
  - pyyaml=6.0=py38h7f8727e_1
  - pyzmq=23.2.0=py38h6a678d5_0
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.1.10=pyhd3eb1b0_0
  - qt=5.15.9=h06a4308_0
  - qt-main=5.15.2=h327a75a_7
  - qt-webengine=5.15.9=hd2b0992_4
  - qtawesome=1.0.3=pyhd3eb1b0_0
  - qtconsole=5.3.2=py38h06a4308_0
  - qtpy=2.2.0=py38h06a4308_0
  - qtwebkit=5.212=h4eab89a_4
  - queuelib=1.5.0=py38h06a4308_0
  - re2=2022.06.01=h27087fc_1
  - readline=8.2=h5eee18b_0
  - regex=2022.7.9=py38h5eee18b_0
  - requests=2.28.1=py38h06a4308_0
  - requests-file=1.5.1=pyhd3eb1b0_0
  - requests-oauthlib=1.3.1=pyhd8ed1ab_0
  - rich=12.6.0=pyhd8ed1ab_0
  - ripgrep=13.0.0=hbdeaff8_0
  - rope=0.22.0=pyhd3eb1b0_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - rtree=0.9.7=py38h06a4308_1
  - ruamel.yaml=0.16.12=py38h5eee18b_3
  - ruamel.yaml.clib=0.2.6=py38h5eee18b_1
  - ruamel_yaml=0.17.21=py38h5eee18b_0
  - s3transfer=0.6.0=py38h06a4308_0
  - scikit-image=0.19.3=py38h6a678d5_1
  - scikit-learn=1.0.2=py38h51133e4_1
  - scikit-learn-intelex=2021.6.0=py38h06a4308_0
  - scipy=1.7.3=py38hc147768_0
  - scrapy=2.6.2=py38h06a4308_0
  - seaborn=0.12.1=py38h06a4308_0
  - secretstorage=3.3.1=py38h06a4308_0
  - send2trash=1.8.0=pyhd3eb1b0_1
  - service_identity=18.1.0=pyhd3eb1b0_1
  - setuptools=65.5.0=py38h06a4308_0
  - sh=1.14.3=pyhd8ed1ab_0
  - shapely=1.8.4=py38h81ba7c5_0
  - sip=6.6.2=py38h6a678d5_0
  - six=1.16.0=pyhd3eb1b0_1
  - smart_open=5.2.1=py38h06a4308_0
  - snappy=1.1.9=h295c915_0
  - sniffio=1.2.0=py38h06a4308_1
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcollections=2.1.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.3.2.post1=py38h06a4308_0
  - sphinx=5.0.2=py38h06a4308_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - spyder=5.3.3=py38h06a4308_0
  - spyder-kernels=2.3.3=py38h06a4308_0
  - sqlalchemy=1.4.39=py38h5eee18b_0
  - sqlite=3.40.0=h5082296_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - statsmodels=0.13.5=py38h7deecbd_1
  - sympy=1.11.1=py38h578d9bd_2
  - tabulate=0.8.10=py38h06a4308_0
  - tbb=2021.6.0=hdb19cb5_0
  - tbb4py=2021.6.0=py38hdb19cb5_0
  - tblib=1.7.0=pyhd3eb1b0_0
  - tenacity=8.0.1=py38h06a4308_1
  - tensorboard=2.11.0=pyhd8ed1ab_0
  - tensorboard-data-server=0.6.1=py38h80a4ca7_4
  - tensorboard-plugin-wit=1.8.1=pyhd8ed1ab_0
  - terminado=0.13.1=py38h06a4308_0
  - testpath=0.6.0=py38h06a4308_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tifffile=2021.7.2=pyhd3eb1b0_2
  - timm=0.6.12=pyhd8ed1ab_0
  - tinycss=0.4=pyhd3eb1b0_1002
  - tinycss2=1.2.1=py38h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - tldextract=3.2.0=pyhd3eb1b0_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=py38h06a4308_0
  - tomlkit=0.11.1=py38h06a4308_0
  - toolz=0.12.0=py38h06a4308_0
  - torchaudio=0.13.0=py38_cu116
  - torchmetrics=0.11.0=pyhd8ed1ab_0
  - torchvision=0.14.0=py38_cu116
  - tornado=6.2=py38h5eee18b_0
  - tqdm=4.64.1=py38h06a4308_0
  - traitlets=5.1.1=pyhd3eb1b0_0
  - twisted=22.2.0=py38h5eee18b_1
  - typed-ast=1.4.3=py38h7f8727e_1
  - typing-extensions=4.4.0=py38h06a4308_0
  - typing_extensions=4.4.0=py38h06a4308_0
  - ujson=5.4.0=py38h6a678d5_0
  - ukkonen=1.0.1=py38h43d8883_2
  - unidecode=1.2.0=pyhd3eb1b0_0
  - unixodbc=2.3.11=h5eee18b_0
  - urllib3=1.26.12=py38h06a4308_0
  - virtualenv=20.17.0=py38h578d9bd_0
  - w3lib=1.21.0=pyhd3eb1b0_0
  - watchdog=2.1.6=py38h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py38_1
  - websocket-client=0.58.0=py38h06a4308_4
  - werkzeug=2.0.3=pyhd3eb1b0_0
  - wget=1.21.4=h251f7ec_1
  - whatthepatch=1.0.2=py38h06a4308_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - widgetsnbextension=3.5.2=py38h06a4308_0
  - wrapt=1.14.1=py38h5eee18b_0
  - wurlitzer=3.0.2=py38h06a4308_0
  - xarray=2022.9.0=pyhd8ed1ab_0
  - xlrd=2.0.1=pyhd3eb1b0_0
  - xlsxwriter=3.0.3=pyhd3eb1b0_0
  - xz=5.2.8=h5eee18b_0
  - yaml=0.2.5=h7b6447c_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - yarl=1.8.1=py38h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zfp=0.5.5=h295c915_6
  - zict=2.1.0=py38h06a4308_0
  - zipp=3.8.0=py38h06a4308_0
  - zlib=1.2.13=hd590300_5
  - zope=1.0=py38_1
  - zope.interface=5.4.0=py38h7f8727e_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
    - alembic==1.8.1
    - autopage==0.5.1
    - cliff==4.1.0
    - cmaes==0.9.0
    - cmd2==2.4.2
    - colorlog==6.7.0
    - hydra-colorlog==1.2.0
    - hydra-optuna-sweeper==1.2.0
    - jsmin==3.0.1
    - mako==1.2.4
    - optuna==2.10.1
    - pbr==5.11.0
    - prettytable==3.5.0
    - pyperclip==1.8.2
    - pyrootutils==1.0.4
    - pysteps==1.7.4
    - python-dotenv==0.21.0
    - stevedore==4.1.1
prefix: /home/<USER>/envs/ddwp
