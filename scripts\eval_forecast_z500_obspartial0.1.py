"""
Z500（500百帕地转位势高度场）中期天气预报评估脚本 - 10%观测覆盖率版本。

这个脚本执行以下操作：
1. 列出系统中的某些目录内容，用于调试和环境检查
2. 使用10%观测覆盖率对不同模型进行中期天气预报能力评估
3. 在评估完成后打印确认信息

评估的模型包括：
1. ViT (Vision Transformer)模型
2. 4DVar-Net模型
3. 4DVar-CycleGAN模型（带尺度参数化）
4. 传统4DVar方法（带通胀因子）

预报评估参数：
- 预热时间：48小时（2天）
- 去相关时长：168小时（7天）
- 观测覆盖率：10%（obs_partial=0.1）

4DVar特定参数：
- 通胀因子：0.5
- 最大迭代次数：1

使用方法：
    直接运行此脚本，无需额外参数。脚本会按顺序评估所有指定的模型。

依赖：
    - Python 3.x
    - 项目特定的评估脚本（位于 src/evaluate/eval_medium_forecast.py）
    - 相关的模型检查点和数据文件

注意：
    - 确保在运行此脚本之前，所有必要的模型检查点和数据文件都已就位
    - 此脚本专门用于中等观测覆盖率（10%）的情况
    - 与5%覆盖率版本使用相同的预热时间（48小时）和去相关时长（7天）
"""

import os  # 导入os模块，用于执行系统命令

# 使用os.system()执行一系列shell命令
os.system(""" 
    # 列出 /home 目录的内容
    ls /home && 
    
    echo "show code" && 
    # 列出 /tmp/code 目录的内容
    ls /tmp/code && 
    
    echo "show dataset" && 
    # 列出 /tmp/dataset 目录的内容
    ls /tmp/dataset && 
    
    echo "show output" && 
    # 列出 /tmp/output 目录的内容
    ls /tmp/output  && 
    
    # 评估ViT模型的中期预报能力，10%观测覆盖率
    python src/evaluate/eval_medium_forecast.py \
        --spinup_hours=48 \
        --decorrelation_hours=168 \
        --model_name=vit \
        --obs_partial=0.1 && \\
    
    # 评估4DVar-Net模型的中期预报能力，10%观测覆盖率
    python src/evaluate/eval_medium_forecast.py \
        --spinup_hours=48 \
        --decorrelation_hours=168 \
        --model_name=4dvarnet \
        --obs_partial=0.1 && \\
    
    # 评估4DVar-CycleGAN模型（带尺度参数化）的中期预报能力，10%观测覆盖率
    python src/evaluate/eval_medium_forecast.py \
        --spinup_hours=48 \
        --decorrelation_hours=168 \
        --model_name=4dvarcyclegan_wscale \
        --obs_partial=0.1 && \\         
    
    # 评估传统4DVar方法（带通胀因子）的中期预报能力，10%观测覆盖率
    python src/evaluate/eval_medium_forecast.py \
        --spinup_hours=48 \
        --decorrelation_hours=168 \
        --model_name=4dvar \
        --inflation=0.5 \
        --maxIter=1 \
        --obs_partial=0.1 && \\
    
    # 打印评估完成信息
    echo "train done" 
""")