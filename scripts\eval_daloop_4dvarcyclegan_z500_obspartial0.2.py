"""
4DVarCycleGAN模型在Z500（500百帕地转位势高度场）数据同化循环评估脚本。

这个脚本执行以下操作：
1. 列出系统中的某些目录内容，用于调试和环境检查。
2. 使用不同配置（有/无尺度参数化）对多个4DVar变种模型进行数据同化循环评估。
3. 所有评估使用20%的观测覆盖率（obs_partial=0.2）。
4. 在评估完成后打印确认信息。

评估的模型包括：
- 4DVar-UNet（有/无尺度参数化）
- 4DVar-GAN（有/无尺度参数化）
- 4DVar-CycleGAN（有/无尺度参数化）

数据同化循环参数：
- 循环时长：720小时
- 去相关时长：2880小时

使用方法：
    直接运行此脚本，无需额外参数。脚本会使用预定义的配置执行评估过程。

依赖：
    - Python 3.x
    - 项目特定的评估脚本（位于 src/evaluate/eval_daloop.py）
    - 相关的模型检查点和数据文件

注意：
    - 确保在运行此脚本之前，所有必要的模型检查点和数据文件都已就位。
    - 此脚本假设在特定的环境中运行，可能需要根据实际部署环境进行调整。
"""

import os  # 导入os模块，用于执行系统命令

# 使用os.system()执行一系列shell命令
os.system(""" 
    # 列出 /home 目录的内容
    ls /home && 
    
    echo "show code" && 
    # 列出 /tmp/code 目录的内容
    ls /tmp/code && 
    
    echo "show dataset" && 
    # 列出 /tmp/dataset 目录的内容
    ls /tmp/dataset && 
    
    echo "show output" && 
    # 列出 /tmp/output 目录的内容
    ls /tmp/output  && 
    
    # 评估4DVar-UNet模型（无尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvarunet_woscale --obs_partial=0.2 && \\
    
    # 评估4DVar-UNet模型（有尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvarunet_wscale --obs_partial=0.2 && \\
    
    # 评估4DVar-GAN模型（无尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvargan_woscale --obs_partial=0.2 && \\    
    
    # 评估4DVar-GAN模型（有尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvargan_wscale --obs_partial=0.2 && \\         
    
    # 评估4DVar-CycleGAN模型（无尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvarcyclegan_woscale --obs_partial=0.2 && \\         
    
    # 评估4DVar-CycleGAN模型（有尺度参数化），观测覆盖率20%
    python src/evaluate/eval_daloop.py --cycle_hours=720 --decorrelation_hours=2880 --model_name=4dvarcyclegan_wscale --obs_partial=0.2 && \\         
    
    # 打印评估完成信息
    echo "train done" 
""")