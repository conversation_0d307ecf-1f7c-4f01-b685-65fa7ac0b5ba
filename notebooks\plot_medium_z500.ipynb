{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 分析Z500上各个模型的性能"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "from src.utils.plot import subplot_daloop\n", "from src.utils.data_utils import NAME_TO_VAR"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["FORECAST_DIR = \"../../output/medium_forecast\"\n", "ERA5_DIR = \"../../data/era5\"\n", "VARIABLE = \"geopotential\"\n", "LEVEL = 500\n", "RESOLUTION = 5.625"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["gt = xr.open_mfdataset(f\"{ERA5_DIR}/{VARIABLE}_{LEVEL}_{RESOLUTION}deg/test/*.nc\", combine=\"by_coords\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 绘制同化预报循环误差"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["rmse_4dvar_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/rmse_4dvar_obspartial0.2.nc\", combine=\"by_coords\")\n", "rmse_4dvarnet_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/rmse_4dvarnet_obspartial0.2.nc\", combine=\"by_coords\")\n", "rmse_vit_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/rmse_vit_obspartial0.2.nc\", combine=\"by_coords\")\n", "rmse_4dvarcyclegan_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/rmse_4dvarcyclegan_wscale_obspartial0.2.nc\", combine=\"by_coords\")\n", "\n", "acc_4dvar_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/acc_4dvar_obspartial0.2.nc\", combine=\"by_coords\")\n", "acc_4dvarnet_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/acc_4dvarnet_obspartial0.2.nc\", combine=\"by_coords\")\n", "acc_vit_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/acc_vit_obspartial0.2.nc\", combine=\"by_coords\")\n", "acc_4dvarcyclegan_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/acc_4dvarcyclegan_wscale_obspartial0.2.nc\", combine=\"by_coords\")\n", "\n", "mae_4dvar_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/mae_4dvar_obspartial0.2.nc\", combine=\"by_coords\")\n", "mae_4dvarnet_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/mae_4dvarnet_obspartial0.2.nc\", combine=\"by_coords\")\n", "mae_vit_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/mae_vit_obspartial0.2.nc\", combine=\"by_coords\")\n", "mae_4dvarcyclegan_obs20 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.2/mae_4dvarcyclegan_wscale_obspartial0.2.nc\", combine=\"by_coords\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["rmse_4dvar_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/rmse_4dvar_obspartial0.1.nc\", combine=\"by_coords\")\n", "rmse_4dvarnet_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/rmse_4dvarnet_obspartial0.1.nc\", combine=\"by_coords\")\n", "rmse_vit_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/rmse_vit_obspartial0.1.nc\", combine=\"by_coords\")\n", "rmse_4dvarcyclegan_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/rmse_4dvarcyclegan_wscale_obspartial0.1.nc\", combine=\"by_coords\")\n", "\n", "acc_4dvar_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/acc_4dvar_obspartial0.1.nc\", combine=\"by_coords\")\n", "acc_4dvarnet_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/acc_4dvarnet_obspartial0.1.nc\", combine=\"by_coords\")\n", "acc_vit_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/acc_vit_obspartial0.1.nc\", combine=\"by_coords\")\n", "acc_4dvarcyclegan_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/acc_4dvarcyclegan_wscale_obspartial0.1.nc\", combine=\"by_coords\")\n", "\n", "mae_4dvar_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/mae_4dvar_obspartial0.1.nc\", combine=\"by_coords\")\n", "mae_4dvarnet_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/mae_4dvarnet_obspartial0.1.nc\", combine=\"by_coords\")\n", "mae_vit_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/mae_vit_obspartial0.1.nc\", combine=\"by_coords\")\n", "mae_4dvarcyclegan_obs15 = xr.open_mfdataset(f\"{DALOOP_DIR}/obs_partial_0.1/mae_4dvarcyclegan_wscale_obspartial0.1.nc\", combine=\"by_coords\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["rmse_4dvar_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                     combine=\"by_coords\")\n", "rmse_4dvarnet_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                        combine=\"by_coords\")\n", "rmse_vit_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                   combine=\"by_coords\")\n", "rmse_4dvarcyclegan_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                             combine=\"by_coords\")\n", "\n", "acc_4dvar_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                     combine=\"by_coords\")\n", "acc_4dvarnet_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                        combine=\"by_coords\")\n", "acc_vit_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                   combine=\"by_coords\")\n", "acc_4dvarcyclegan_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                             combine=\"by_coords\")\n", "\n", "mae_4dvar_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                     combine=\"by_coords\")\n", "mae_4dvarnet_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                        combine=\"by_coords\")\n", "mae_vit_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                   combine=\"by_coords\")\n", "mae_4dvarcyclegan_obs10 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.1/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                             combine=\"by_coords\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["rmse_4dvar_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                     combine=\"by_coords\")\n", "rmse_4dvarnet_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                        combine=\"by_coords\")\n", "rmse_vit_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                   combine=\"by_coords\")\n", "rmse_4dvarcyclegan_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/rmse*.nc\", \n", "                                             combine=\"by_coords\")\n", "\n", "acc_4dvar_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                     combine=\"by_coords\")\n", "acc_4dvarnet_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                        combine=\"by_coords\")\n", "acc_vit_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                   combine=\"by_coords\")\n", "acc_4dvarcyclegan_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/acc*.nc\", \n", "                                             combine=\"by_coords\")\n", "\n", "mae_4dvar_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvar_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                     combine=\"by_coords\")\n", "mae_4dvarnet_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarnet_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                        combine=\"by_coords\")\n", "mae_vit_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_vit_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                   combine=\"by_coords\")\n", "mae_4dvarcyclegan_obs5 = xr.open_mfdataset(f\"{FORECAST_DIR}/obs_partial_0.05/evaluate_4dvarcyclegan_wscale_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}/mae*.nc\", \n", "                                             combine=\"by_coords\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["rmse = {\n", "    \"4DVar Obs20\": rmse_4dvar_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarNet Obs20\": rmse_4dvarnet_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"ViT Obs20\": rmse_vit_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarGAN Obs20\": rmse_4dvarcyclegan_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "\n", "    # \"4DVar Obs15\": rmse_4dvar_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs15\": rmse_4dvarnet_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs15\": rmse_vit_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs15\": rmse_4dvarcyclegan_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs10\": rmse_4dvar_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs10\": rmse_4dvarnet_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs10\": rmse_vit_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs10\": rmse_4dvarcyclegan_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs5\": rmse_4dvar_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs5\": rmse_4dvarnet_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs5\": rmse_vit_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs5\": rmse_4dvarcyclegan_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "}\n", "\n", "acc = {\n", "    \"4DVar Obs20\": acc_4dvar_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarNet Obs20\": acc_4dvarnet_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"ViT Obs20\": acc_vit_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarGAN Obs20\": acc_4dvarcyclegan_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "\n", "    # \"4DVar Obs15\": acc_4dvar_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs15\": acc_4dvarnet_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs15\": acc_vit_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs15\": acc_4dvarcyclegan_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs10\": acc_4dvar_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs10\": acc_4dvarnet_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs10\": acc_vit_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs10\": acc_4dvarcyclegan_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs5\": acc_4dvar_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs5\": acc_4dvarnet_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs5\": acc_vit_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs5\": acc_4dvarcyclegan_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "}\n", "\n", "mae = {\n", "    \"4DVar Obs20\": mae_4dvar_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarNet Obs20\": mae_4dvarnet_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"ViT Obs20\": mae_vit_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "    \"4DVarGAN Obs20\": mae_4dvarcyclegan_obs20[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values[8:],\n", "\n", "    # \"4DVar Obs15\": mae_4dvar_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs15\": mae_4dvarnet_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs15\": mae_vit_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs15\": mae_4dvarcyclegan_obs15[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs10\": mae_4dvar_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs10\": mae_4dvarnet_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs10\": mae_vit_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs10\": mae_4dvarcyclegan_obs10[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "\n", "    # \"4DVar Obs5\": mae_4dvar_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarNet Obs5\": mae_4dvarnet_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"ViT Obs5\": mae_vit_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "    # \"4DVarGAN Obs5\": mae_4dvarcyclegan_obs5[NAME_TO_VAR[f\"{VARIABLE}_{LEVEL}\"]].values,\n", "}"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x900 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axes = plt.subplots(3, 4, figsize=(14, 9))\n", "axes[0,0].plot(rmse[\"4DVar Obs20\"], label=f\"4DVar\")\n", "axes[0,0].plot(rmse[\"4DVarNet Obs20\"], label=f\"4DVarNet\")\n", "axes[0,0].plot(rmse[\"ViT Obs20\"], label=f\"ViT\")\n", "axes[0,0].plot(rmse[\"4DVarGAN Obs20\"], label=f\"4DVarGAN\")\n", "# axes[0,1].plot(rmse[\"4DVar Obs15\"], label=f\"4DVar\")\n", "# axes[0,1].plot(rmse[\"4DVarNet Obs15\"], label=f\"4DVarNet\")\n", "# axes[0,1].plot(rmse[\"ViT Obs15\"], label=f\"ViT\")\n", "# axes[0,1].plot(rmse[\"4DVarGAN Obs15\"], label=f\"4DVarGAN\")\n", "# axes[0,2].plot(rmse[\"4DVar Obs10\"], label=f\"4DVar\")\n", "# axes[0,2].plot(rmse[\"4DVarNet Obs10\"], label=f\"4DVarNet\")\n", "# axes[0,2].plot(rmse[\"ViT Obs10\"], label=f\"ViT\")\n", "# axes[0,2].plot(rmse[\"4DVarGAN Obs10\"], label=f\"4DVarGAN\")\n", "# axes[0,3].plot(rmse[\"4DVar Obs5\"], label=f\"4DVar\")\n", "# axes[0,3].plot(rmse[\"4DVarNet Obs5\"], label=f\"4DVarNet\")\n", "# axes[0,3].plot(rmse[\"ViT Obs5\"], label=f\"ViT\")\n", "# axes[0,3].plot(rmse[\"4DVarGAN Obs5\"], label=f\"4DVarGAN\")\n", "\n", "axes[1,0].plot(acc[\"4DVar Obs20\"], label=f\"4DVar\")\n", "axes[1,0].plot(acc[\"4DVarNet Obs20\"], label=f\"4DVarNet\")\n", "axes[1,0].plot(acc[\"ViT Obs20\"], label=f\"ViT\")\n", "axes[1,0].plot(acc[\"4DVarGAN Obs20\"], label=f\"4DVarGAN\")\n", "# axes[1,1].plot(acc[\"4DVar Obs15\"], label=f\"4DVar\")\n", "# axes[1,1].plot(acc[\"4DVarNet Obs15\"], label=f\"4DVarNet\")\n", "# axes[1,1].plot(acc[\"ViT Obs15\"], label=f\"ViT\")\n", "# axes[1,1].plot(acc[\"4DVarGAN Obs15\"], label=f\"4DVarGAN\")\n", "# axes[1,2].plot(acc[\"4DVar Obs10\"], label=f\"4DVar\")\n", "# axes[1,2].plot(acc[\"4DVarNet Obs10\"], label=f\"4DVarNet\")\n", "# axes[1,2].plot(acc[\"ViT Obs10\"], label=f\"ViT\")\n", "# axes[1,2].plot(acc[\"4DVarGAN Obs10\"], label=f\"4DVarGAN\")\n", "# axes[1,3].plot(acc[\"4DVar Obs5\"], label=f\"4DVar\")\n", "# axes[1,3].plot(acc[\"4DVarNet Obs5\"], label=f\"4DVarNet\")\n", "# axes[1,3].plot(acc[\"ViT Obs5\"], label=f\"ViT\")\n", "# axes[1,3].plot(acc[\"4DVarGAN Obs5\"], label=f\"4DVarGAN\")\n", "\n", "axes[2,0].plot(mae[\"4DVar Obs20\"], label=f\"4DVar\")\n", "axes[2,0].plot(mae[\"4DVarNet Obs20\"], label=f\"4DVarNet\")\n", "axes[2,0].plot(mae[\"ViT Obs20\"], label=f\"ViT\")\n", "axes[2,0].plot(mae[\"4DVarGAN Obs20\"], label=f\"4DVarGAN\")\n", "# axes[2,1].plot(mae[\"4DVar Obs15\"], label=f\"4DVar\")\n", "# axes[2,1].plot(mae[\"4DVarNet Obs15\"], label=f\"4DVarNet\")\n", "# axes[2,1].plot(mae[\"ViT Obs15\"], label=f\"ViT\")\n", "# axes[2,1].plot(mae[\"4DVarGAN Obs15\"], label=f\"4DVarGAN\")\n", "# axes[2,2].plot(mae[\"4DVar Obs10\"], label=f\"4DVar\")\n", "# axes[2,2].plot(mae[\"4DVarNet Obs10\"], label=f\"4DVarNet\")\n", "# axes[2,2].plot(mae[\"ViT Obs10\"], label=f\"ViT\")\n", "# axes[2,2].plot(mae[\"4DVarGAN Obs10\"], label=f\"4DVarGAN\")\n", "# axes[2,3].plot(mae[\"4DVar Obs5\"], label=f\"4DVar\")\n", "# axes[2,3].plot(mae[\"4DVarNet Obs5\"], label=f\"4DVarNet\")\n", "# axes[2,3].plot(mae[\"ViT Obs5\"], label=f\"ViT\")\n", "# axes[2,3].plot(mae[\"4DVarGAN Obs5\"], label=f\"4DVarGAN\")\n", "\n", "for i in range(4):\n", "    axes[0,i].set_ylim(300, 1050)\n", "    axes[1,i].set_ylim(0.45, 1)\n", "    axes[2,i].set_ylim(200, 650)\n", "    axes[0,i].set_title(f\"{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL} {int(20-5*i)}% Observation\")\n", "    for j in range(3):\n", "        axes[j,i].set_xticks(np.arange(0, 32, 4))\n", "        axes[j,i].set_xticklabels([0, 1, 2, 3, 4, 5, 6, 7])\n", "\n", "axes[0,0].set_ylabel(f\"RMSE [m$^2$ s$^{-2}$]\")\n", "axes[1,0].set_ylabel(f\"ACC\")\n", "axes[2,0].set_ylabel(f\"RMSE [m$^2$ s$^{-2}$]\")\n", "lines, labels = fig.axes[0].get_legend_handles_labels()\n", "fig.legend(lines, labels, ncol=4, loc='lower center', bbox_to_anchor=(0.425, 0))\n", "fig.text(0.5, 0.05, f\"Lead Times [days]\", ha='center')\n", "plt.savefig(f\"forecast_{NAME_TO_VAR[f'{VARIABLE}_{LEVEL}']}{LEVEL}.pdf\",dpi=300, bbox_inches=\"tight\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["(36,)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["rmse[\"4DVar Obs20\"].shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ddwp", "language": "python", "name": "ddwp"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.15"}}, "nbformat": 4, "nbformat_minor": 4}