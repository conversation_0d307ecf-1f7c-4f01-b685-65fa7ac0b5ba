name: gan
channels:
  - pytorch
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - absl-py=1.3.0=pyhd8ed1ab_0
  - aiohttp=3.8.1=py37h540881e_1
  - aiosignal=1.3.1=pyhd8ed1ab_0
  - antlr-python-runtime=4.9.3=pyhd8ed1ab_1
  - async-timeout=4.0.2=pyhd8ed1ab_0
  - asynctest=0.13.0=py_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - beautifulsoup4=4.9.3=pyha847dfd_0
  - blas=1.0=mkl
  - blinker=1.5=pyhd8ed1ab_0
  - brotlipy=0.7.0=py37h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - c-ares=1.18.1=h7f8727e_0
  - ca-certificates=2023.11.17=hbcca054_0
  - cachetools=5.2.0=pyhd8ed1ab_0
  - cartopy=0.19.0.post1=py37h0c48da3_1
  - certifi=2023.11.17=pyhd8ed1ab_0
  - cffi=1.14.5=py37h261ae71_0
  - cfgv=3.3.1=pyhd8ed1ab_0
  - cftime=*******=py37hce1f21e_0
  - chardet=3.0.4=py37h06a4308_1003
  - charset-normalizer=2.1.1=pyhd8ed1ab_0
  - click=8.1.3=py37h89c1867_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - commonmark=0.9.1=py_0
  - conda=23.1.0=py37h06a4308_0
  - conda-build=3.23.2=py37h06a4308_0
  - conda-package-handling=1.7.3=py37h27cfd23_1
  - cryptography=3.4.7=py37hd23ed53_0
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - curl=7.84.0=h5eee18b_0
  - cycler=0.11.0=pyhd8ed1ab_0
  - dataclasses=0.8=pyhc8e2a94_3
  - dbus=1.13.6=he372182_0
  - decorator=5.0.9=pyhd3eb1b0_0
  - distlib=0.3.6=pyhd8ed1ab_0
  - einops=0.6.1=pyhd8ed1ab_0
  - exceptiongroup=1.0.4=pyhd8ed1ab_0
  - expat=2.4.8=h27087fc_0
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.8.0=pyhd8ed1ab_0
  - fontconfig=2.14.0=h8e229c2_0
  - freetype=2.10.4=h5ab3b9f_0
  - frozenlist=1.3.3=py37h5eee18b_0
  - fsspec=2022.11.0=pyhd8ed1ab_0
  - future=0.18.2=py37_0
  - geos=3.9.1=h9c3ff4c_2
  - glib=2.69.1=h4ff587b_1
  - glob2=0.7=pyhd3eb1b0_0
  - gmp=6.2.1=h2531618_2
  - gnutls=3.6.15=he1e5248_0
  - google-auth=2.15.0=pyh1a96a4e_0
  - google-auth-oauthlib=0.4.6=pyhd8ed1ab_0
  - grpcio=1.42.0=py37hce63b2e_0
  - gst-plugins-base=1.14.0=hbbd80ab_1
  - gstreamer=1.14.1=h5eee18b_1
  - hdf4=4.2.13=h3ca952b_2
  - hdf5=1.10.6=nompi_h6a2412b_1114
  - huggingface_hub=0.11.1=pyhd8ed1ab_0
  - hydra-core=1.2.0=pyhd8ed1ab_0
  - icu=58.2=he6710b0_3
  - identify=2.5.9=pyhd8ed1ab_0
  - idna=2.10=py_0
  - importlib-metadata=4.11.3=py37h06a4308_0
  - importlib_metadata=4.11.3=hd3eb1b0_0
  - importlib_resources=5.10.0=pyhd8ed1ab_0
  - iniconfig=1.1.1=pyh9f0ad1d_0
  - intel-openmp=2021.2.0=h06a4308_610
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - jedi=0.17.0=py37_0
  - jpeg=9b=h024ee3a_2
  - keyutils=1.6.1=h166bdaf_0
  - kiwisolver=1.4.2=py37h7cecad7_1
  - krb5=1.19.3=h3790be6_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.33.1=h53a641e_7
  - libarchive=3.4.2=h62408e4_0
  - libblas=3.9.0=9_mkl
  - libcblas=3.9.0=9_mkl
  - libcurl=7.84.0=h91b91d3_0
  - libedit=3.1.20191231=h14c3975_1
  - libev=4.33=h516909a_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=12.2.0=h69a702a_19
  - libgfortran5=12.2.0=h337968e_19
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.15=h63c8f33_5
  - libidn2=2.3.1=h27cfd23_0
  - liblapack=3.9.0=9_mkl
  - liblief=0.10.1=he6710b0_0
  - libnetcdf=4.6.1=h2053bdc_4
  - libnghttp2=1.46.0=hce63b2e_0
  - libpng=1.6.37=hbc83047_0
  - libprotobuf=3.20.1=h4ff587b_0
  - libssh2=1.10.0=ha56f1ee_2
  - libstdcxx-ng=12.2.0=h46fd767_19
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=2.32.1=h7f98852_1000
  - libuv=1.40.0=h7b6447c_0
  - libwebp-base=1.2.0=h27cfd23_0
  - libxcb=1.13=h7f98852_1004
  - libxml2=2.9.10=hb55368b_3
  - lz4-c=1.9.3=h2531618_0
  - markdown=3.4.1=pyhd8ed1ab_0
  - markupsafe=2.0.1=py37h27cfd23_0
  - matplotlib=3.4.3=py37h89c1867_2
  - matplotlib-base=3.4.3=py37h1058ff1_0
  - mkl=2021.2.0=h06a4308_296
  - mkl-service=2.3.0=py37h27cfd23_1
  - mkl_fft=1.3.0=py37h42c9631_2
  - mkl_random=1.2.1=py37ha9443f7_2
  - multidict=6.0.2=py37h540881e_1
  - ncurses=6.2=he6710b0_1
  - netcdf4=1.5.7=py37h0a24e14_0
  - nettle=3.7.3=hbbd107a_1
  - ninja=1.10.2=hff7bd54_1
  - nodeenv=1.7.0=pyhd8ed1ab_0
  - numpy=1.20.2=py37h2d18471_0
  - numpy-base=1.20.2=py37hfae3a4d_0
  - oauthlib=3.2.2=pyhd8ed1ab_0
  - olefile=0.46=py37_0
  - omegaconf=2.2.3=py37h89c1867_0
  - openh264=2.1.0=hd408876_0
  - openssl=1.1.1w=h7f8727e_0
  - packaging=21.3=pyhd8ed1ab_0
  - pandas=1.2.3=py37hdc94413_0
  - parso=0.8.2=pyhd3eb1b0_0
  - patch=2.7.6=h7b6447c_1001
  - patchelf=0.12=h2531618_1
  - pcre=8.45=h9c3ff4c_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=8.2.0=py37he98fc37_0
  - pip=21.1.2=py37h06a4308_0
  - pkginfo=1.7.0=py37h06a4308_0
  - platformdirs=2.5.2=pyhd8ed1ab_1
  - pluggy=1.0.0=py37h06a4308_1
  - portalocker=2.6.0=py37h89c1867_0
  - pre-commit=2.20.0=py37h89c1867_0
  - proj=7.2.0=h8b9fe22_0
  - prompt-toolkit=3.0.17=pyh06a4308_0
  - protobuf=3.20.1=py37h295c915_0
  - psutil=5.8.0=py37h27cfd23_1
  - pthread-stubs=0.4=h36c2ea0_1001
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - py-lief=0.10.1=py37h403a769_0
  - pyasn1=0.4.8=py_0
  - pyasn1-modules=0.2.7=py_0
  - pycosat=0.6.3=py37h27cfd23_0
  - pycparser=2.20=py_2
  - pydeprecate=0.3.2=pyhd8ed1ab_0
  - pygments=2.9.0=pyhd3eb1b0_0
  - pyjwt=2.6.0=pyhd8ed1ab_0
  - pyopenssl=20.0.1=pyhd8ed1ab_0
  - pyqt=5.9.2=py37hcca6a23_4
  - pyshp=2.3.1=pyhd8ed1ab_0
  - pysocks=1.7.1=py37_1
  - pytest=7.2.0=py37h89c1867_0
  - python=3.7.10=hdb3f193_0
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python-libarchive-c=2.9=pyhd3eb1b0_1
  - python_abi=3.7=2_cp37m
  - pytorch=1.12.1=py3.7_cuda11.3_cudnn8.3.2_0
  - pytorch-lightning=1.7.7=pyhd8ed1ab_0
  - pytorch-mutex=1.0=cuda
  - pytz=2021.1=pyhd3eb1b0_0
  - pyu2f=0.1.5=pyhd8ed1ab_0
  - pyyaml=5.4.1=py37h27cfd23_1
  - qt=5.9.7=h5867ecd_1
  - readline=8.0=h7b6447c_0
  - requests=2.24.0=py_0
  - requests-oauthlib=1.3.1=pyhd8ed1ab_0
  - rich=12.6.0=pyhd8ed1ab_0
  - ripgrep=12.1.1=0
  - rsa=4.9=pyhd8ed1ab_0
  - ruamel.yaml=0.16.12=py37h7b6447c_1
  - ruamel.yaml.clib=0.2.6=py37h7f8727e_0
  - ruamel_yaml=0.15.100=py37h27cfd23_0
  - scipy=1.7.3=py37hf2a6cf1_0
  - setuptools=52.0.0=py37h06a4308_0
  - sh=1.14.2=py37h89c1867_0
  - shapely=1.8.0=py37h48c49eb_0
  - sip=4.19.8=py37hf484d3e_1000
  - six=1.16.0=pyhd3eb1b0_0
  - soupsieve=2.2.1=pyhd3eb1b0_0
  - sqlite=3.33.0=h62c20be_0
  - tensorboard=2.11.0=pyhd8ed1ab_0
  - tensorboard-data-server=0.6.0=py37hca6d32c_0
  - tensorboard-plugin-wit=1.8.1=pyhd8ed1ab_0
  - timm=0.6.12=pyhd8ed1ab_0
  - tk=8.6.10=hbc83047_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=pyhd8ed1ab_0
  - toolz=0.12.0=py37h06a4308_0
  - torchaudio=0.12.1=py37_cu113
  - torchdata=0.4.1=pyh8b8bddf_0
  - torchmetrics=0.11.0=pyhd8ed1ab_0
  - torchtext=0.13.1=py37
  - torchvision=0.13.1=py37_cu113
  - tornado=6.1=py37h540881e_3
  - tqdm=4.64.1=pyhd8ed1ab_0
  - typing-extensions=4.4.0=hd8ed1ab_0
  - typing_extensions=4.4.0=pyha770c72_0
  - ukkonen=1.0.1=py37h7cecad7_2
  - urllib3=1.25.11=py_0
  - virtualenv=20.16.2=py37h06a4308_0
  - wcwidth=0.2.5=py_0
  - werkzeug=2.1.2=pyhd8ed1ab_1
  - wheel=0.35.1=pyhd3eb1b0_0
  - xarray=0.20.2=pyhd8ed1ab_0
  - xorg-libxau=1.0.9=h7f98852_0
  - xorg-libxdmcp=1.1.3=h7f98852_0
  - xz=5.2.5=h7b6447c_0
  - yaml=0.2.5=h7b6447c_0
  - yarl=1.7.2=py37h540881e_2
  - zipp=3.8.0=py37h06a4308_0
  - zlib=1.2.12=h7f8727e_2
  - zstd=1.4.9=haebb681_0
  - pip:
      - alembic==1.8.1
      - anyio==3.6.2
      - argon2-cffi==21.3.0
      - argon2-cffi-bindings==21.2.0
      - attrs==21.4.0
      - autopage==0.5.1
      - babel==2.11.0
      - bleach==5.0.0
      - bokeh==2.4.3
      - cliff==3.10.1
      - cloudpickle==2.2.0
      - cmaes==0.9.0
      - cmd2==2.4.2
      - colorlog==6.7.0
      - dask==2022.2.0
      - debugpy==1.6.0
      - defusedxml==0.7.1
      - distributed==2022.2.0
      - dnspython==2.1.0
      - entrypoints==0.4
      - fastjsonschema==2.15.3
      - greenlet==2.0.1
      - h5py==3.8.0
      - heapdict==1.0.1
      - hydra-colorlog==1.2.0
      - hydra-optuna-sweeper==1.2.0
      - importlib-resources==5.7.1
      - ipykernel==6.13.0
      - ipython==7.32.0
      - jinja2==3.1.2
      - jsmin==3.0.1
      - json5==0.9.6
      - jsonschema==4.4.0
      - jupyter-client==7.3.0
      - jupyter-core==4.10.0
      - jupyter-server==1.23.3
      - jupyterlab==3.0.0
      - jupyterlab-pygments==0.2.2
      - jupyterlab-server==2.16.3
      - locket==1.0.0
      - mako==1.2.4
      - matplotlib-inline==0.1.3
      - mistune==0.8.4
      - msgpack==1.0.4
      - nbclassic==1.0.0
      - nbclient==0.6.0
      - nbconvert==6.5.0
      - nbformat==5.3.0
      - nest-asyncio==1.5.5
      - notebook==6.4.11
      - notebook-shim==0.2.3
      - optuna==2.10.1
      - pandocfilters==1.5.0
      - partd==1.3.0
      - pbr==5.11.0
      - prettytable==3.5.0
      - prometheus-client==0.14.1
      - pyparsing==3.0.8
      - pyperclip==1.8.2
      - pyrootutils==1.0.4
      - pyrsistent==0.18.1
      - pysteps==1.7.4
      - python-dotenv==0.21.0
      - python-etcd==0.4.5
      - pyzmq==22.3.0
      - send2trash==1.8.0
      - sniffio==1.3.0
      - sortedcontainers==2.4.0
      - sqlalchemy==1.4.44
      - stevedore==3.5.2
      - tblib==1.7.0
      - terminado==0.13.3
      - tinycss2==1.1.1
      - torchelastic==0.2.0
      - traitlets==5.1.1
      - webencodings==0.5.1
      - websocket-client==1.4.2
      - zict==2.2.0
prefix: /opt/conda
