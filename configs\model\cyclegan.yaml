_target_: src.models.assimilate.cyclegan_assim_module.AssimilateLitModule

g_A2B:
  _target_: src.models.assimilate.cyclegan.genDA_unet.UNet
  in_channels: 4
  hidden_channels: 32
  out_channels: 1
  num_downsampling: 2
  num_resnet_blocks: 4
  init_type: 'normal'
  init_gain: 0.02

g_B2A:
  _target_: src.models.assimilate.cyclegan.genB_unet.UNet
  in_channels: 1
  hidden_channels: 32
  out_channels: 1
  num_downsampling: 2
  num_resnet_blocks: 4
  init_type: 'normal'
  init_gain: 0.02

d_A:
  _target_: src.models.assimilate.cyclegan.patch_discriminator.PatchDiscriminator
  in_channels: 1
  hidden_channels: 32
  num_layers: 3
  init_type: normal
  init_gain: 0.02

d_B:
  _target_: src.models.assimilate.cyclegan.patch_discriminator.PatchDiscriminator
  in_channels: 4
  hidden_channels: 32
  num_layers: 3
  init_type: normal
  init_gain: 0.02

g_optimizer:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 5e-3
  betas: [0.5, 0.999]

d_optimizer:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 2e-3
  betas: [0.5, 0.999]

scheduler:
  _target_: src.utils.train_utils.GradualWarmupScheduler
  _partial_: true
  multiplier: 1
  total_epoch: 5

after_scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  _partial_: true
  T_max: 45
  eta_min: 1e-8
  verbose: True

mean_path: ${paths.era5_dir}/normalize_mean.npy
std_path: ${paths.era5_dir}/normalize_std.npy
clim_paths:
  - ${paths.era5_dir}/train/climatology.npy
  - ${paths.era5_dir}/val/climatology.npy
  - ${paths.era5_dir}/test/climatology.npy

loss:
  _target_: src.utils.train_utils.CycleGANLoss
  loss_type: MSE
  rec_loss:
    _target_: torch.nn.L1Loss
  lambda_cyc: 10
  lambda_idt: 10
  lambda_rec: 100
  lambda_adv: 1