#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""数据同化UNet生成器模块。

此模块实现了一个专门用于数据同化的UNet生成器，其特点是：
1. 同时接收背景场和观测数据作为输入
2. 使用周期性填充处理全球大气场
3. 通过残差连接提高特征提取能力
4. 使用实例归一化处理不同样本的统计特性

主要组件：
    - UNet: 主生成器架构，用于数据同化
    - ResBlock: 残差块，用于特征提取
    - L2Norm: L2范数层，用于损失计算

@author: wuxinwang
"""

# 导入必要的库
from functools import partial  # 用于创建偏函数
import numpy as np  # 数值计算
import torch  # PyTorch深度学习框架
from torch import nn  # 神经网络模块
import torch.nn.functional as F  # 函数式接口
from src.utils.model_utils import PeriodicPad2d  # 周期性填充

class L2Norm(torch.nn.Module):
    def __init__(self):
        super(L2Norm, self).__init__()

    def forward(self,x):
        loss_ = torch.nansum(x**2 , dim=-1)
        loss_ = torch.nansum(loss_, dim=-1)
        loss_ = torch.nansum(loss_, dim=1)

        return loss_

class ResBlock(nn.Module):

    def __init__(self, in_channels: int, apply_dropout: bool = True):

        """
                            Defines a ResBlock
        X ------------------------identity------------------------
        |-- Convolution -- Norm -- ReLU -- Convolution -- Norm --|
        """

        super().__init__()

        conv = nn.Conv2d(in_channels=in_channels, out_channels=in_channels, kernel_size=3, stride=1)
        layers =  [PeriodicPad2d(1), conv, nn.InstanceNorm2d(in_channels), nn.ReLU(True)]

        if apply_dropout:
            layers += [nn.Dropout(0.5)]

        conv = nn.Conv2d(in_channels=in_channels, out_channels=in_channels, kernel_size=3, stride=1)
        layers += [PeriodicPad2d(1), conv, nn.InstanceNorm2d(in_channels)]

        self.net = nn.Sequential(*layers)

    def forward(self, x): 
        return x + self.net(x)

class UNet(nn.Module):
    """Implements the ViT model,

    Args:
        default_vars (list): list of default variables to be used for training
        img_size (list): image size of the input data
        patch_size (int): patch size of the input data
        embed_dim (int): embedding dimension
        depth (int): number of transformer layers
        decoder_depth (int): number of decoder layers
        num_heads (int): number of attention heads
        mlp_ratio (float): ratio of mlp hidden dimension to embedding dimension
        drop_path (float): stochastic depth rate
        drop_rate (float): dropout rate
    """

    def __init__(
        self,
        in_channels: int = 1,
        hidden_channels: int = 64,
        out_channels: int = 1,
        apply_dropout: bool = True,
        num_downsampling: int = 2,
        num_resnet_blocks: int = 4,
        init_type: str = 'normal',
        init_gain: float = 0.02,
    ):
        super().__init__()

        # TODO: remove time_history parameter
        f = 1
        num_downsampling = num_downsampling
        num_resnet_blocks = num_resnet_blocks
        self.init_type = init_type
        self.init_gain = init_gain
        
        conv = nn.Conv2d(in_channels=in_channels, out_channels=hidden_channels, kernel_size=7, stride=1)
        self.layers = [PeriodicPad2d(3), conv, nn.InstanceNorm2d(hidden_channels), nn.ReLU(True)]

        for i in range(num_downsampling):
            conv = nn.Conv2d(hidden_channels * f, hidden_channels * 2 * f, kernel_size=3, stride=2)
            self.layers += [PeriodicPad2d(1), conv, nn.InstanceNorm2d(hidden_channels * 2 * f), nn.ReLU(True)]
            f *= 2

        for i in range(num_resnet_blocks):
            resnet_block = ResBlock(in_channels = hidden_channels * f, apply_dropout = apply_dropout)
            self.layers += [resnet_block]

        for i in range(num_downsampling):
            conv = nn.ConvTranspose2d(hidden_channels * f, hidden_channels * (f//2), 3, 2, padding=1, output_padding=1)
            self.layers += [conv, nn.InstanceNorm2d(hidden_channels * (f//2)), nn.ReLU(True)]
            f = f // 2

        conv = nn.Conv2d(in_channels=hidden_channels, out_channels=out_channels, kernel_size=7, stride=1)
        self.layers += [PeriodicPad2d(3), conv]

        self.net = nn.Sequential(*self.layers)

    def init_module(self, m):
        cls_name = m.__class__.__name__
        if hasattr(m, 'weight') and (cls_name.find('Conv') != -1 or cls_name.find('Linear') != -1):

            if self.init_type == 'kaiming':
                nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
            elif self.init_type == 'xavier':
                nn.init.xavier_normal_(m.weight.data, gain=self.init_gain)
            elif self.init_type == 'normal':
                nn.init.normal_(m.weight.data, mean=0, std=self.init_gain)
            else:
                raise ValueError('Initialization not found!!')

            if m.bias is not None: nn.init.constant_(m.bias.data, val=0);

        if hasattr(m, 'weight') and cls_name.find('BatchNorm2d') != -1:
            nn.init.normal_(m.weight.data, mean=1.0, std=self.init_gain)
            nn.init.constant_(m.bias.data, val=0)

    def forward(self, xb, obs, obs_mask):
        """模型前向传播。

        将背景场和观测数据作为输入，通过UNet结构生成分析场。
        观测数据通过掩码进行筛选，只使用有效的观测点。

        Args:
            xb (torch.Tensor): 背景场，形状为 [B, C, H, W]
                - B: 批次大小
                - C: 通道数
                - H: 高度
                - W: 宽度
            obs (torch.Tensor): 观测场，形状与xb相同
            obs_mask (torch.Tensor): 观测掩码，形状与xb相同
                - 1表示有效观测
                - 0表示无效观测

        Returns:
            torch.Tensor: 生成的分析场，形状与输入相同
        """
        # 将背景场和有效观测数据拼接作为输入
        # obs * obs_mask 只保留有效观测点的值
        input_tensor = torch.concat([xb, obs * obs_mask], dim=1)
        
        # 通过UNet网络生成分析场
        preds = self.net(input_tensor)

        return preds