classdef NCDataset < handle
    % NCDataset - A dataset class for handling NetCDF data for assimilation tasks
    % This class loads and processes NetCDF data for background fields, observations,
    % and analysis fields for data assimilation applications.
    
    properties
        % Data directories
        era5_dir
        background_dir
        obs_dir
        obs_mask_dir
        
        % Configuration parameters
        init_time
        obs_partial
        mode
        var
        pred_len
        random_erase
        
        % Data storage
        era5_files
        background_files
        obs_files
        obs_mask_files
        
        % Time information
        time_list
        train_time_list
        val_time_list
        test_time_list
    end
    
    methods
        function obj = NCDataset(varargin)
            % Constructor for NCDataset
            % Inputs (as name-value pairs):
            %   era5_dir - Directory containing ERA5 data
            %   background_dir - Directory containing background data
            %   obs_dir - Directory containing observation data
            %   obs_mask_dir - Directory containing observation mask data
            %   init_time - Initial time
            %   obs_partial - Observation partial value
            %   mode - Dataset mode ('train', 'val', or 'test')
            %   var - Variable name
            %   pred_len - Prediction length
            %   random_erase - Boolean flag for random erase
            
            % Parse inputs
            p = inputParser;
            addParameter(p, 'era5_dir', '', @ischar);
            addParameter(p, 'background_dir', '', @ischar);
            addParameter(p, 'obs_dir', '', @ischar);
            addParameter(p, 'obs_mask_dir', '', @ischar);
            addParameter(p, 'init_time', '', @ischar);
            addParameter(p, 'obs_partial', 0.2, @isnumeric);
            addParameter(p, 'mode', 'train', @ischar);
            addParameter(p, 'var', 'z', @ischar);
            addParameter(p, 'pred_len', 1, @isnumeric);
            addParameter(p, 'random_erase', false, @islogical);
            parse(p, varargin{:});
            
            % Store parameters
            obj.era5_dir = p.Results.era5_dir;
            obj.background_dir = p.Results.background_dir;
            obj.obs_dir = p.Results.obs_dir;
            obj.obs_mask_dir = p.Results.obs_mask_dir;
            obj.init_time = p.Results.init_time;
            obj.obs_partial = p.Results.obs_partial;
            obj.mode = p.Results.mode;
            obj.var = p.Results.var;
            obj.pred_len = p.Results.pred_len;
            obj.random_erase = p.Results.random_erase;
            
            % Initialize data
            obj.setup_data();
        end
        
        function setup_data(obj)
            % Set up data files and time lists
            
            % Get all ERA5 files
            era5_pattern = fullfile(obj.era5_dir, '*.nc');
            era5_files_list = dir(era5_pattern);
            obj.era5_files = {era5_files_list.name};
            
            % Get all background files
            bg_pattern = fullfile(obj.background_dir, '*.nc');
            bg_files_list = dir(bg_pattern);
            obj.background_files = {bg_files_list.name};
            
            % Get all observation files
            obs_pattern = fullfile(obj.obs_dir, sprintf('*_partial%.1f.nc', obj.obs_partial));
            obs_files_list = dir(obs_pattern);
            obj.obs_files = {obs_files_list.name};
            
            % Get all observation mask files
            mask_pattern = fullfile(obj.obs_mask_dir, sprintf('*_partial%.1f.nc', obj.obs_partial));
            mask_files_list = dir(mask_pattern);
            obj.obs_mask_files = {mask_files_list.name};
            
            % Extract time information from filenames
            obj.time_list = cellfun(@(x) extract_time_from_filename(x), obj.era5_files, 'UniformOutput', false);
            
            % Filter by init_time if specified
            if ~isempty(obj.init_time)
                init_datetime = datetime(obj.init_time, 'InputFormat', 'yyyy-MM-dd');
                time_datetimes = cellfun(@(x) datetime(x, 'InputFormat', 'yyyyMMdd'), obj.time_list);
                valid_indices = time_datetimes >= init_datetime;
                
                obj.time_list = obj.time_list(valid_indices);
                obj.era5_files = obj.era5_files(valid_indices);
                obj.background_files = obj.background_files(valid_indices);
                obj.obs_files = obj.obs_files(valid_indices);
                obj.obs_mask_files = obj.obs_mask_files(valid_indices);
            end
            
            % Sort all lists by time
            [obj.time_list, sort_idx] = sort(obj.time_list);
            obj.era5_files = obj.era5_files(sort_idx);
            obj.background_files = obj.background_files(sort_idx);
            obj.obs_files = obj.obs_files(sort_idx);
            obj.obs_mask_files = obj.obs_mask_files(sort_idx);
            
            % Split into train, validation, and test sets (70%, 15%, 15%)
            n_total = length(obj.time_list);
            n_train = floor(0.7 * n_total);
            n_val = floor(0.15 * n_total);
            
            obj.train_time_list = obj.time_list(1:n_train);
            obj.val_time_list = obj.time_list(n_train+1:n_train+n_val);
            obj.test_time_list = obj.time_list(n_train+n_val+1:end);
        end
        
        function n = length(obj)
            % Return the number of samples in the dataset
            % Output:
            %   n - Number of samples
            
            switch obj.mode
                case 'train'
                    n = length(obj.train_time_list);
                case 'val'
                    n = length(obj.val_time_list);
                case 'test'
                    n = length(obj.test_time_list);
                otherwise
                    error('Invalid mode: %s', obj.mode);
            end
        end
        
        function sample = subsref(obj, s)
            % Implement indexing for the dataset
            % Input:
            %   s - Subscript structure
            % Output:
            %   sample - The requested sample
            
            % Handle different types of indexing
            switch s(1).type
                case '()'
                    % Handle parentheses indexing: obj(idx)
                    idx = s(1).subs{1};
                    if length(s) == 1
                        % Direct indexing: obj(idx)
                        sample = obj.get_item(idx);
                    else
                        % Pass remaining subscripts to the result
                        result = obj.get_item(idx);
                        s(1) = [];
                        sample = subsref(result, s);
                    end
                case '.'
                    % Handle dot indexing: obj.property
                    if length(s) == 1
                        % Direct property access
                        switch s(1).subs
                            case properties(obj)
                                sample = obj.(s(1).subs);
                            case methods(obj)
                                sample = @(varargin) obj.(s(1).subs)(varargin{:});
                            otherwise
                                error('Unknown property or method: %s', s(1).subs);
                        end
                    else
                        % Property access followed by further indexing
                        result = obj.(s(1).subs);
                        s(1) = [];
                        sample = subsref(result, s);
                    end
                otherwise
                    error('Unsupported indexing type: %s', s(1).type);
            end
        end
        
        function sample = get_item(obj, idx)
            % Get a specific sample from the dataset
            % Input:
            %   idx - Index of the sample to retrieve
            % Output:
            %   sample - The requested sample as a cell array {xb, obs, obs_mask, xt}
            
            % Get the appropriate time based on the mode
            switch obj.mode
                case 'train'
                    time = obj.train_time_list{idx};
                    time_idx = find(strcmp(obj.time_list, time));
                case 'val'
                    time = obj.val_time_list{idx};
                    time_idx = find(strcmp(obj.time_list, time));
                case 'test'
                    time = obj.test_time_list{idx};
                    time_idx = find(strcmp(obj.time_list, time));
                otherwise
                    error('Invalid mode: %s', obj.mode);
            end
            
            % Load data files
            era5_file = fullfile(obj.era5_dir, obj.era5_files{time_idx});
            bg_file = fullfile(obj.background_dir, obj.background_files{time_idx});
            obs_file = fullfile(obj.obs_dir, obj.obs_files{time_idx});
            mask_file = fullfile(obj.obs_mask_dir, obj.obs_mask_files{time_idx});
            
            % Read NetCDF data
            era5_data = ncread(era5_file, obj.var);
            bg_data = ncread(bg_file, obj.var);
            obs_data = ncread(obs_file, obj.var);
            mask_data = ncread(mask_file, 'mask');
            
            % Apply random erase if enabled
            if obj.random_erase
                [obs_data, mask_data] = random_erase_func(obs_data, mask_data);
            end
            
            % Prepare the sample
            % Note: In MATLAB, we'll use 3D arrays instead of PyTorch tensors
            % The dimensions will be [height, width, channels] instead of [channels, height, width]
            % We'll transpose the data to match the expected format
            
            % Reshape data to have the right dimensions
            % Assuming the data is 2D (height x width)
            xb = reshape(bg_data, [size(bg_data, 1), size(bg_data, 2), 1]);
            obs = reshape(obs_data, [size(obs_data, 1), size(obs_data, 2), 1]);
            obs_mask = reshape(mask_data, [size(mask_data, 1), size(mask_data, 2), 1]);
            xt = reshape(era5_data, [size(era5_data, 1), size(era5_data, 2), 1]);
            
            % Return the sample as a cell array
            sample = {xb, obs, obs_mask, xt};
        end
    end
end

function time_str = extract_time_from_filename(filename)
    % Extract time information from a filename
    % Input:
    %   filename - Name of the file
    % Output:
    %   time_str - Extracted time string
    
    % Assuming filename format contains a date like 'YYYYMMDD'
    % Extract using regular expression
    tokens = regexp(filename, '(\d{8})', 'tokens');
    if ~isempty(tokens)
        time_str = tokens{1}{1};
    else
        time_str = '';
    end
end

function [obs_erased, mask_erased] = random_erase_func(obs, mask, erase_ratio)
    % Apply random erasure to observation data and mask
    % Inputs:
    %   obs - Observation data
    %   mask - Observation mask
    %   erase_ratio - Ratio of data to erase (default: 0.2)
    % Outputs:
    %   obs_erased - Erased observation data
    %   mask_erased - Updated mask
    
    if nargin < 3
        erase_ratio = 0.2;
    end
    
    % Create random mask for erasure
    erase_mask = rand(size(mask)) > erase_ratio;
    
    % Apply erasure
    mask_erased = mask .* erase_mask;
    obs_erased = obs;  % Keep original observations, only update the mask
    
    % Alternatively, if we want to zero out the erased observations:
    % obs_erased = obs .* mask_erased;
end
