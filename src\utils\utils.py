"""
Utility functions for the project.

This module provides various utility functions for:
1. Task execution and management
2. Logger configuration and management
3. Callback instantiation
4. Hyperparameter logging
5. Metric value retrieval
6. Network port management

The utilities in this module are designed to work with PyTorch Lightning and Hydra frameworks,
providing essential functionality for training deep learning models.
"""

import time
import warnings
from importlib.util import find_spec
from pathlib import Path
from typing import Any, Callable, Dict, List

import hydra
from omegaconf import DictConfig
from pytorch_lightning import Callback
from pytorch_lightning.loggers import LightningLoggerBase
from pytorch_lightning.utilities import rank_zero_only
import argparse
from src.utils import pylogger, rich_utils

from contextlib import closing
import socket

# Initialize logger for this module
log = pylogger.get_pylogger(__name__)


def task_wrapper(task_func: Callable) -> Callable:
    """Decorator that wraps a task function with additional utilities for robust execution.

    This wrapper provides several utilities to make the task execution more robust:
    1. Applies extra utilities before task start
    2. Handles exceptions and logs them
    3. Measures and logs execution time
    4. Ensures proper logger closure
    5. Logs output directory information

    Args:
        task_func (Callable): The task function to be wrapped

    Returns:
        Callable: The wrapped function with added utilities
    """
    """Optional decorator that wraps the task function in extra utilities.

    Makes multirun more resistant to failure.

    Utilities:
    - Calling the `utils.extras()` before the task is started
    - Calling the `utils.close_loggers()` after the task is finished
    - Logging the exception if occurs
    - Logging the task total execution time
    - Logging the output dir
    """

    def wrap(cfg: DictConfig):
        """Inner wrapper function that executes the task with added utilities.
        
        Args:
            cfg (DictConfig): Configuration object containing task parameters
            
        Returns:
            tuple: Containing metric dictionary and object dictionary
        """
        # Apply extra utilities before task execution
        extras(cfg)

        # execute the task
        try:
            start_time = time.time()
            metric_dict, object_dict = task_func(cfg=cfg)
        except Exception as ex:
            log.exception("")  # save exception to `.log` file
            raise ex
        finally:
            path = Path(cfg.paths.output_dir, "exec_time.log")
            content = f"'{cfg.task_name}' execution time: {time.time() - start_time} (s)"
            save_file(path, content)  # save task execution time (even if exception occurs)
            close_loggers()  # close loggers (even if exception occurs so multirun won't fail)

        log.info(f"Output dir: {cfg.paths.output_dir}")

        return metric_dict, object_dict

    return wrap


def extras(cfg: DictConfig) -> None:
    """Apply optional utilities before task execution.
    
    This function handles several optional utilities:
    1. Python warning management
    2. Command line tag enforcement
    3. Rich config printing
    
    Args:
        cfg (DictConfig): Configuration object containing extras settings
    """
    """Applies optional utilities before the task is started.

    Utilities:
    - Ignoring python warnings
    - Setting tags from command line
    - Rich config printing
    """

    # return if no `extras` config
    if not cfg.get("extras"):
        log.warning("Extras config not found! <cfg.extras=null>")
        return

    # disable python warnings
    if cfg.extras.get("ignore_warnings"):
        log.info("Disabling python warnings! <cfg.extras.ignore_warnings=True>")
        warnings.filterwarnings("ignore")

    # prompt user to input tags from command line if none are provided in the config
    if cfg.extras.get("enforce_tags"):
        log.info("Enforcing tags! <cfg.extras.enforce_tags=True>")
        rich_utils.enforce_tags(cfg, save_to_file=True)

    # pretty print config tree using Rich library
    if cfg.extras.get("print_config"):
        log.info("Printing config tree with Rich! <cfg.extras.print_config=True>")
        rich_utils.print_config_tree(cfg, resolve=True, save_to_file=True)


@rank_zero_only
def save_file(path, content) -> None:
    """Save content to a file in rank zero mode.
    
    This function is decorated with @rank_zero_only to ensure it only executes
    on the main process in a distributed training setup.
    
    Args:
        path: Path where the file should be saved
        content: Content to write to the file
    """
    """Save file in rank zero mode (only on one process in multi-GPU setup)."""
    with open(path, "w+") as file:
        file.write(content)


def instantiate_callbacks(callbacks_cfg: DictConfig) -> List[Callback]:
    """Create callback objects from configuration.
    
    This function instantiates PyTorch Lightning callbacks based on the provided
    configuration dictionary.
    
    Args:
        callbacks_cfg (DictConfig): Configuration for callbacks
        
    Returns:
        List[Callback]: List of instantiated callback objects
        
    Raises:
        TypeError: If callbacks_cfg is not a DictConfig
    """
    """Instantiates callbacks from config."""
    callbacks: List[Callback] = []

    if not callbacks_cfg:
        log.warning("Callbacks config is empty.")
        return callbacks

    if not isinstance(callbacks_cfg, DictConfig):
        raise TypeError("Callbacks config must be a DictConfig!")

    for _, cb_conf in callbacks_cfg.items():
        if isinstance(cb_conf, DictConfig) and "_target_" in cb_conf:
            log.info(f"Instantiating callback <{cb_conf._target_}>")
            callbacks.append(hydra.utils.instantiate(cb_conf))

    return callbacks


def instantiate_loggers(logger_cfg: DictConfig) -> List[LightningLoggerBase]:
    """Create logger objects from configuration.
    
    This function instantiates PyTorch Lightning loggers based on the provided
    configuration dictionary.
    
    Args:
        logger_cfg (DictConfig): Configuration for loggers
        
    Returns:
        List[LightningLoggerBase]: List of instantiated logger objects
        
    Raises:
        TypeError: If logger_cfg is not a DictConfig
    """
    """Instantiates loggers from config."""
    logger: List[LightningLoggerBase] = []

    if not logger_cfg:
        log.warning("Logger config is empty.")
        return logger

    if not isinstance(logger_cfg, DictConfig):
        raise TypeError("Logger config must be a DictConfig!")

    for _, lg_conf in logger_cfg.items():
        if isinstance(lg_conf, DictConfig) and "_target_" in lg_conf:
            log.info(f"Instantiating logger <{lg_conf._target_}>")
            logger.append(hydra.utils.instantiate(lg_conf))

    return logger


@rank_zero_only
def log_hyperparameters(object_dict: Dict[str, Any]) -> None:
    """Log hyperparameters using lightning loggers.
    
    This function controls which configuration parts are saved by lightning loggers.
    It specifically logs model parameters, datamodule configuration, trainer settings,
    and other relevant hyperparameters.
    
    Args:
        object_dict (Dict[str, Any]): Dictionary containing model, trainer, and config objects
    """
    """Controls which config parts are saved by lightning loggers.

    Additionally saves:
    - Number of model parameters
    """

    hparams = {}

    cfg = object_dict["cfg"]
    model = object_dict["model"]
    trainer = object_dict["trainer"]

    if not trainer.logger:
        log.warning("Logger not found! Skipping hyperparameter logging...")
        return

    hparams["model"] = cfg["model"]

    # save number of model parameters
    hparams["model/params/total"] = sum(p.numel() for p in model.parameters())
    hparams["model/params/trainable"] = sum(
        p.numel() for p in model.parameters() if p.requires_grad
    )
    hparams["model/params/non_trainable"] = sum(
        p.numel() for p in model.parameters() if not p.requires_grad
    )

    hparams["datamodule"] = cfg["datamodule"]
    hparams["trainer"] = cfg["trainer"]

    hparams["callbacks"] = cfg.get("callbacks")
    hparams["extras"] = cfg.get("extras")

    hparams["task_name"] = cfg.get("task_name")
    hparams["tags"] = cfg.get("tags")
    hparams["ckpt_path"] = cfg.get("ckpt_path")
    hparams["seed"] = cfg.get("seed")

    # send hparams to all loggers
    trainer.logger.log_hyperparams(hparams)


def get_metric_value(metric_dict: dict, metric_name: str) -> float:
    """Safely retrieve metric value from metric dictionary.
    
    This function provides a safe way to get metric values logged in LightningModule,
    with proper error handling and logging.
    
    Args:
        metric_dict (dict): Dictionary containing metric values
        metric_name (str): Name of the metric to retrieve
        
    Returns:
        float: Value of the specified metric
        
    Raises:
        Exception: If metric_name is not found in metric_dict
    """
    """Safely retrieves value of the metric logged in LightningModule."""

    if not metric_name:
        log.info("Metric name is None! Skipping metric value retrieval...")
        return None

    if metric_name not in metric_dict:
        raise Exception(
            f"Metric value not found! <metric_name={metric_name}>\n"
            "Make sure metric name logged in LightningModule is correct!\n"
            "Make sure `optimized_metric` name in `hparams_search` config is correct!"
        )

    metric_value = metric_dict[metric_name].item()
    log.info(f"Retrieved metric value! <{metric_name}={metric_value}>")

    return metric_value


def close_loggers() -> None:
    """Ensure proper closure of all loggers.
    
    This function is particularly important for preventing logging failures
    during multirun experiments. It specifically handles the closure of
    wandb logger if it's installed and running.
    """
    """Makes sure all loggers closed properly (prevents logging failure during multirun)."""

    log.info("Closing loggers...")

    if find_spec("wandb"):  # if wandb is installed
        import wandb

        if wandb.run:
            log.info("Closing wandb!")
            wandb.finish()

def str2bool(v):
    """Convert string representation of boolean to actual boolean.
    
    Args:
        v: Input value to convert (string or boolean)
        
    Returns:
        bool: Converted boolean value
        
    Raises:
        argparse.ArgumentTypeError: If input cannot be converted to boolean
    """
    if isinstance(v, bool):
        return v
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')

def get_open_port():
    """Find and return an available network port.
    
    This function creates a temporary socket, binds it to port 0 (letting the OS choose),
    and returns the assigned port number.
    
    Returns:
        int: An available port number
    """
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        return s.getsockname()[1]

def isInuse(ipList, port):
    """Check if a port is in use across multiple IP addresses.
    
    Args:
        ipList (list): List of IP addresses to check
        port (int): Port number to check
        
    Returns:
        bool: True if port is in use, False otherwise
    """
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    flag=True
    for ip in ipList:
        try:
            s.connect((ip, int(port)))
            s.shutdown(2)
            print('%d is inuse' % port)
            flag=True
            break
        except:
            print('%d is free' % port)
            flag=False
    return flag

def getLocalIp():
    """Get the local IP address of the machine.
    
    Returns:
        str: Local IP address
    """
    localIP = socket.gethostbyname(socket.gethostname())
    return localIP

def checkNinePort(startPort):
    """Check availability of a range of ports starting from startPort.
    
    Args:
        startPort (int): Starting port number to check
        
    Returns:
        tuple: (bool, int) indicating success status and ending port number
    """
    flag = True
    ipList = ("127.0.0.1","0.0.0.0",getLocalIp())
    for i in range(1, 16):
        if (isInuse(ipList, startPort)):
            flag = False
            break
        else:
            startPort = startPort + 1
    return flag, startPort

def find_free_ports(startPort):
    """Find a range of consecutive free ports starting from startPort.
    
    This function keeps searching until it finds a range of available ports
    that can be used for the application.
    
    Args:
        startPort (int): Initial port number to start searching from
        
    Returns:
        int: First port number in the found range of available ports
    """
    while True:
        flag, endPort = checkNinePort(startPort)
        if (flag == True):  #ninePort is ok
            break
        else:
            startPort = endPort + 1
    return startPort
