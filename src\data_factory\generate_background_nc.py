# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
import torch
from src.utils.data_utils import NAME_TO_VAR
from src.models.forecast.forecast_module import ForecastLitModule
from src.evaluate.inference import autoregressive_inference_background
from c2net.context import prepare,upload_output

#初始化导入数据集和预训练模型到容器内
c2net_context = prepare()

HOURS_PER_YEAR = 8760  # 365-day year

VARIABLES = [
  "geopotential_500",
#   "temperature_850"  
] 

def nc2np(path,
          variable,
          years,
          save_dir,
          model,
          lead_time,
          decorrelation_time,
          resolution,
          mean,
          std,
          device):

    for year in tqdm(years):
        # non-constant fields
        combined_ds = xr.Dataset()
        ps = glob.glob(os.path.join(f"{path}", f"{variable}_{resolution}deg", f"*{year}*.nc"))
        ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True)  # dataset for a single variable
        code = NAME_TO_VAR[variable]
        combined_ds[code] = ds[code]

        for coord in ds.coords:
            if coord not in combined_ds.coords:
                combined_ds[coord] = ds[coord]
        
        n_samples_all = len(combined_ds[NAME_TO_VAR[variable]])
        ics = np.arange(lead_time, n_samples_all, decorrelation_time)   

        fcs = []
        for i, ic in enumerate(ics):
            fc = autoregressive_inference_background(ic, mean, std, combined_ds, model, lead_time, 24, variable, device)
            fcs.append(fc)
            del fc
        
        fc_iter = xr.concat(fcs, dim="time")
        
        print(fc_iter)
        
        fc_iter.to_netcdf(os.path.join(save_dir, f"{variable}hPa_{year}_{resolution}deg.nc"))

@click.command()
@click.option(
    "--variable",
    "-v",
    type=click.STRING,
    # multiple=True,
    default="geopotential_500",
        # "temperature_850",
)
@click.option("--resolution", type=float, default=5.625)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
@click.option("--lead_time", type=int, default=24)
@click.option("--decorrelation_time", type=int, default=6)
@click.option("--model_name", type=str, default="afnonet_z500")
def main(
    variable,
    resolution,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
    lead_time,
    decorrelation_time,
    model_name,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    #获取数据集路径
    era5_path = c2net_context.dataset_path+"/"+"era5/era5"
    root_dir = c2net_context.dataset_path+"/"+f"{variable}_{resolution}deg"
    pretrain_ckpt = c2net_context.dataset_path+"/"+"ckpt"

    #输出结果必须保存在该目录
    save_dir = c2net_context.output_path

    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)
    device = "cuda" if torch.cuda.is_available else "cpu"
    mean = np.load(os.path.join(era5_path, f"{variable}_{resolution}deg", "normalize_mean.npy"))
    std = np.load(os.path.join(era5_path, f"{variable}_{resolution}deg", "normalize_std.npy"))
    
    forecast_model = ForecastLitModule.load_from_checkpoint(f"{pretrain_ckpt}/{model_name}.ckpt",
                                                           mean_path=str(os.path.join(era5_path, f"{variable}_{resolution}deg", "normalize_mean.npy")),
                                                           std_path=str(os.path.join(era5_path, f"{variable}_{resolution}deg", "normalize_std.npy")),
                                                           clim_paths=[
                                                               str(os.path.join(era5_path, f"{variable}_{resolution}deg", "train/climatology.npy")),
                                                               str(os.path.join(era5_path, f"{variable}_{resolution}deg", "val/climatology.npy")),
                                                               str(os.path.join(era5_path, f"{variable}_{resolution}deg", "test/climatology.npy"))
                                                           ])
    model = forecast_model.net.to(device).eval()

    nc2np(root_dir, variable, train_years, save_dir, model, lead_time, decorrelation_time, resolution, mean, std, device)
    nc2np(root_dir, variable, val_years, save_dir, model, lead_time, decorrelation_time, resolution, mean, std, device)
    nc2np(root_dir, variable, test_years, save_dir, model, lead_time, decorrelation_time, resolution, mean, std, device)

    #回传结果到openi，只有训练任务才能回传
    upload_output()

if __name__ == "__main__":
    main()
