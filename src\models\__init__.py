"""
Models Package for 4DVarGAN_V2

This package contains the model implementations for both data assimilation and forecasting tasks.
The models are organized into two main submodules:

1. assimilate/
    - Contains CycleGAN-based models for data assimilation
    - Includes implementations of generators and discriminators
    - Main components:
        - cyclegan/: Generator and discriminator architectures
        - cyclegan_assim_module.py: Lightning module for assimilation

2. forecast/
    - Contains models for weather forecasting
    - Implements AFNONet architecture and related components
    - Main components:
        - afnonet/: AFNONet model architecture
        - layers/: Custom neural network layers
        - forecast_module.py: Lightning module for forecasting

The models in this package are designed to work with PyTorch Lightning for 
training and inference, and are configured using Hydra for hyperparameter management.

Note:
    All models follow PyTorch Lightning conventions and can be used with the 
    training and evaluation scripts provided in the project.
"""