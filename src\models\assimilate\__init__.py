"""数据同化模型模块。

此模块包含用于气象数据同化的深度学习模型实现，主要基于CycleGAN架构。

主要组件:
    - cyclegan: CycleGAN模型的核心组件，包含生成器和判别器
        - genB_unet.py: 背景场到分析场的UNet生成器
        - genDA_unet.py: 带观测输入的数据同化UNet生成器
        - patch_discriminator.py: PatchGAN判别器
    - cyclegan_assim_module.py: PyTorch Lightning实现的CycleGAN数据同化训练模块

模型特点:
    1. 双向映射机制：实现背景场与分析场之间的双向转换
    2. 观测数据融合：通过掩码机制融合稀疏观测数据
    3. 周期性边界处理：适用于全球气象场的特殊边界条件
    4. 多重损失函数：结合对抗损失、循环一致性损失和重建损失

参考文献:
    - CycleGAN: https://arxiv.org/abs/1703.10593
    - U-Net: https://arxiv.org/abs/1505.04597
    - 数据同化: https://doi.org/10.1016/j.atmosres.2021.105901
"""