# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
from src.utils.data_utils import NAME_TO_VAR

def nc2np(path, var, years, save_dir, resolution, partition, mean):
    
    for year in tqdm(years):
        np_vars = {}

        # non-constant fields
        ps = glob.glob(os.path.join(f"{path}", f"{var}_{resolution}deg", partition, f"*{year}*.nc"))
        ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True)  # dataset for a single variable
        code = NAME_TO_VAR[var]

        # remove the last 24 hours if this year has 366 days
        np_vars[f"{var}"] = ds[code].to_numpy()[::6] 
            
        t, h, w = np_vars[f"{var}"].shape
            
        if var == "geopotential_500":
            np_vars[f"{var}"] += 0.015 * np.random.randn(t, h, w) * mean
        elif var == "temperature_850":
            np_vars[f"{var}"] += np.random.randn(t, h, w)
        
        xr_var = xr.DataArray(
            np_vars[f"{var}"].astype(np.float32),
            dims=['time', 'lat', 'lon'],
            coords={
                'time': ds["time"][::6],
                'lat': ds.lat.values, 
                'lon': ds.lon.values
            },
            name=NAME_TO_VAR[var]
        )
            
        os.makedirs(f"{save_dir}/{var}_{resolution}deg", exist_ok=True)
            
        xr_var.to_netcdf(os.path.join(save_dir, f"{var}_{resolution}deg", partition, f"{var}hPa_{year}_{resolution}deg.nc"))
                
@click.command()
@click.option("--root_dir", type=click.Path(exists=True))
@click.option("--save_dir", type=click.STRING)
@click.option(
    "--variable",
    "-v",
    type=click.STRING,
    default= "geopotential_500", # "temperature_850",
)
@click.option("--resolution", type=float, default=5.625)
@click.option("--mean_path", type=str)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
def main(
    root_dir,
    save_dir,
    variable,
    resolution,
    mean_path,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)
    
    mean = np.load(mean_path)

    nc2np(root_dir, variable, train_years, save_dir, resolution, "train", mean)
    nc2np(root_dir, variable, val_years, save_dir, resolution, "val", mean)
    nc2np(root_dir, variable, test_years, save_dir, resolution, "test", mean)


if __name__ == "__main__":
    main()

