_target_: src.models.forecast.forecast_module.ForecastLitModule

optimizer:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 1e-3
  betas: [0.9, 0.95]
  weight_decay: 0.

scheduler:
  _target_: src.utils.train_utils.GradualWarmupScheduler
  _partial_: true
  multiplier: 1
  total_epoch: 10

after_scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  _partial_: true
  T_max: 90
  eta_min: 1e-8
  verbose: True

net:
  _target_: src.models.forecast.afnonet.arch.AFNONet
  img_size: [32, 64]
  patch_size: 4
  embed_dim: 64
  depth: 12
  num_blocks: 8
  mlp_ratio: 4.0
  drop_path: 0.2
  drop_rate: 0.
  double_skip: true
  sparsity_threshold: 0.01
  hard_thresholding_fraction: 1.0

mean_path: ${paths.era5_dir}/normalize_mean.npy
std_path: ${paths.era5_dir}/normalize_std.npy
clim_paths:
  - ${paths.era5_dir}/train/climatology.npy
  - ${paths.era5_dir}/val/climatology.npy
  - ${paths.era5_dir}/test/climatology.npy
loss:
  _target_: torch.nn.L1Loss