"""PatchGAN判别器实现。

此模块实现了一个PatchGAN判别器，用于判别生成的气象场是否真实。
特点：
1. 使用卷积网络对输入进行patch级别的判别
2. 采用Markovian判别器结构，只关注局部特征
3. 使用实例归一化和LeakyReLU激活
4. 支持多种权重初始化方法

参考文献：
    - PatchGAN: https://arxiv.org/abs/1611.07004
    - CycleGAN: https://arxiv.org/abs/1703.10593
"""

import torch
import torch.nn as nn
from torch.nn.utils import spectral_norm  # 用于权重谱归一化

class PatchDiscriminator(nn.Module):

    def __init__(self,
                in_channels: int = 2,
                hidden_channels: int = 64,
                num_layers: int = 3,
                init_type: str = 'normal',
                init_gain: float = 0.02,
                ):
        """初始化PatchGAN判别器。

        网络结构：C64 - C128 - C256 - C512
        其中Ck表示：带k个滤波器的 Conv-InstanceNorm-LeakyReLU 层

        Args:
            in_channels (int): 输入通道数，默认为2（数据场和观测场）
            hidden_channels (int): 初始特征通道数，默认为64
            num_layers (int): 卷积层数，默认为3
            init_type (str): 权重初始化方法，可选'normal'、'xavier'、'kaiming'
            init_gain (float): 初始化增益，默认为0.02

        注意：
            - 每层将特征图尺寸减半，通道数翻倍
            - 使用InstanceNorm而不是BatchNorm，以处理单个样本
            - 使用LeakyReLU作为激活函数，避免死亡ReLU问题
        """
        super().__init__()
        # 通道缩放因子
        in_f = 1   # 输入特征通道缩放因子
        out_f = 2  # 输出特征通道缩放因子
        
        # 保存初始化参数
        self.init_type = init_type
        self.init_gain = init_gain

        # 第一个卷积块：不使用谱归一化
        conv = nn.Conv2d(in_channels, hidden_channels, kernel_size=4, stride=2, padding=1)
        self.layers = [
            conv,                                           # 4x4卷积，步长2
            nn.InstanceNorm2d(hidden_channels * out_f),    # 实例归一化
            nn.LeakyReLU(0.2, True)                       # LeakyReLU激活
        ]

        # 中间卷积块：逐步增加通道数，减小特征图尺寸
        for idx in range(1, num_layers):
            conv = nn.Conv2d(
                hidden_channels * in_f,     # 输入通道
                hidden_channels * out_f,    # 输出通道（翻倍）
                kernel_size=4,              # 4x4卷积核
                stride=2,                   # 步长2用于下采样
                padding=1                   # 填充保持特征图对齐
            )
            self.layers += [
                conv,
                nn.InstanceNorm2d(hidden_channels * out_f),
                nn.LeakyReLU(0.2, inplace=True)
            ]
            in_f = out_f      # 更新输入通道缩放因子
            out_f *= 2        # 翻倍输出通道缩放因子

        # 最后两个卷积块：保持特征图尺寸不变
        # 1. 特征提取层
        out_f = min(2 ** num_layers, 8)  # 限制最大通道数
        conv = nn.Conv2d(
            hidden_channels * in_f,
            hidden_channels * out_f,
            kernel_size=4,
            stride=1,         # 步长1保持尺寸
            padding=1
        )
        self.layers += [
            conv,
            nn.InstanceNorm2d(hidden_channels * out_f),
            nn.LeakyReLU(0.2, inplace=True)
        ]

        # 2. 输出层：映射到判别分数
        conv = nn.Conv2d(
            hidden_channels * out_f,
            out_channels=1,   # 输出单通道判别分数
            kernel_size=4,
            stride=1,
            padding=1
        )
        self.layers += [conv]

        # 构建完整的网络
        self.net = nn.Sequential(*self.layers)

        # 应用权重初始化
        self.net.apply(self.init_module)

    def init_module(self, m):
        """初始化网络模块的参数。

        支持三种初始化方式：
        - kaiming：适用于ReLU激活的层
        - xavier：适用于tanh激活的层
        - normal：标准正态分布初始化

        Args:
            m (nn.Module): 要初始化的模块
        """
        cls_name = m.__class__.__name__

        # 初始化卷积层和线性层
        if hasattr(m, 'weight') and (cls_name.find('Conv') != -1 or cls_name.find('Linear') != -1):
            # 根据指定方法初始化权重
            if self.init_type == 'kaiming':
                nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')  # He初始化
            elif self.init_type == 'xavier':
                nn.init.xavier_normal_(m.weight.data, gain=self.init_gain)  # Xavier初始化
            elif self.init_type == 'normal':
                nn.init.normal_(m.weight.data, mean=0, std=self.init_gain)  # 正态分布初始化
            else:
                raise ValueError('不支持的初始化方法！')

            # 偏置初始化为0
            if m.bias is not None:
                nn.init.constant_(m.bias.data, val=0)

        # 初始化BatchNorm层
        if hasattr(m, 'weight') and cls_name.find('BatchNorm2d') != -1:
            nn.init.normal_(m.weight.data, mean=1.0, std=self.init_gain)  # 权重初始化
            nn.init.constant_(m.bias.data, val=0)                         # 偏置初始化为0

    def forward(self, x):
        """模型前向传播。

        对输入数据进行patch级别的真实性判别。

        Args:
            x (torch.Tensor): 输入数据，形状为 [B, C, H, W]
                - B: 批次大小
                - C: 输入通道数
                - H: 高度
                - W: 宽度

        Returns:
            torch.Tensor: 判别分数，形状为 [B, 1, H', W']
                - H', W': 降采样后的特征图尺寸
                - 每个位置的值表示对应patch的真实性得分
        """
        return self.net(x)