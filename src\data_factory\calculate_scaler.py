# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
from src.utils.data_utils import NAME_TO_VAR

def nc2np(path, var, years, save_dir, partition, resolution):
    os.makedirs(os.path.join(save_dir, partition), exist_ok=True)

    if partition == "train":
        normalize_mean = []
        normalize_std = []
    climatology = []

    for year in tqdm(years):

        # non-constant fields
        ps = glob.glob(os.path.join(f"{path}", f"{var}_{resolution}deg", f"{partition}", f"*{year}*.nc"))
        ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True)  # dataset for a single variable
        code = NAME_TO_VAR[var]
            
        np_var = ds[code].to_numpy()[::6]

        if partition == "train":  # compute mean and std of each var in each year
            var_mean_yearly = np_var.mean(axis=(0, 1, 2))
            var_std_yearly = np_var.std(axis=(0, 1, 2))
            
            normalize_mean.append(var_mean_yearly)
            normalize_std.append(var_std_yearly)

        clim_yearly = np_var.mean(axis=0)
        climatology.append(clim_yearly)

    if partition == "train":
        mean = np.stack(normalize_mean, axis=0)
        std = np.stack(normalize_std, axis=0)

        # var(X) = E[var(X|Y)] + var(E[X|Y])
        variance = (std**2).mean(axis=0) + (mean**2).mean(axis=0) - mean.mean(axis=0) ** 2
        std = np.sqrt(variance)
        # E[X] = E[E[X|Y]]
        mean = mean.mean(axis=0)

        np.save(os.path.join(save_dir, "normalize_mean.npy"), mean)
        np.save(os.path.join(save_dir, "normalize_std.npy"), std)


    climatology = np.mean(np.stack(climatology, axis=0), axis=0)
    np.save(os.path.join(save_dir, partition, "climatology.npy"), climatology)

@click.command()
@click.option("--root_dir", type=click.Path(exists=True))
@click.option("--save_dir", type=click.STRING)
@click.option(
    "--variable",
    "-v",
    type=click.STRING,
    default="geopotential_500", # "temperature_850",
)
@click.option("--resolution", type=float, default=5.625)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
def main(
    root_dir,
    save_dir,
    variable,
    resolution,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)

    nc2np(root_dir, variable, train_years, save_dir, "train", resolution)
    nc2np(root_dir, variable, val_years, save_dir, "val", resolution)
    nc2np(root_dir, variable, test_years, save_dir, "test", resolution)

if __name__ == "__main__":
    main()

