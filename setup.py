#!/usr/bin/env python

from setuptools import find_packages, setup

setup(
    name="4dvargancyclegan",
    version="2.0.0",
    description="4DVarGAN V2: A deep learning based data assimilation system",
    author="4DVarGAN Team",
    author_email="<EMAIL>",
    url="https://github.com/example/4DVarGAN_V2",
    install_requires=[
        "torch>=1.8.0",
        "pytorch-lightning>=1.5.0",
        "numpy>=1.19.0",
        "xarray>=0.18.0",
        "netCDF4>=1.5.0",
        "matplotlib>=3.3.0",
        "hydra-core>=1.1.0",
        "omegaconf>=2.1.0",
        "scikit-learn>=0.24.0",
    ],
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Atmospheric Science",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.8",
)