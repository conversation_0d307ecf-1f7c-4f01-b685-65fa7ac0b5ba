classdef DataLoader < handle
    % DataLoader - A class for loading batches of data from a dataset
    % This class provides functionality similar to PyTorch's DataLoader,
    % allowing batch loading, shuffling, and multi-threaded data loading.
    
    properties
        dataset          % The dataset to load from
        batch_size       % Size of each batch
        drop_last        % Whether to drop the last incomplete batch
        num_workers      % Number of worker threads
        pin_memory       % Whether to pin memory (not used in MATLAB)
        worker_seed      % Random seed for workers
        prefetch_factor  % Prefetch factor (not fully implemented)
        collate_fn       % Function to collate samples into batches
        
        % Internal properties
        indices          % Indices for dataset access
        current_idx      % Current position in the dataset
        num_batches      % Number of batches
    end
    
    methods
        function obj = DataLoader(dataset, varargin)
            % Constructor for DataLoader
            % Inputs:
            %   dataset - The dataset to load from
            %   varargin - Optional name-value pairs:
            %     'batch_size' - Size of each batch (default: 1)
            %     'drop_last' - Whether to drop the last incomplete batch (default: false)
            %     'num_workers' - Number of worker threads (default: 0)
            %     'pin_memory' - Whether to pin memory (default: false, not used in MATLAB)
            %     'worker_seed' - Random seed for workers (default: 0)
            %     'prefetch_factor' - Prefetch factor (default: 2, not fully implemented)
            %     'collate_fn' - Function to collate samples into batches (default: @default_collate)
            
            % Parse inputs
            p = inputParser;
            addParameter(p, 'batch_size', 1, @isnumeric);
            addParameter(p, 'drop_last', false, @islogical);
            addParameter(p, 'num_workers', 0, @isnumeric);
            addParameter(p, 'pin_memory', false, @islogical);
            addParameter(p, 'worker_seed', 0, @isnumeric);
            addParameter(p, 'prefetch_factor', 2, @isnumeric);
            addParameter(p, 'collate_fn', @default_collate, @(x) isa(x, 'function_handle'));
            parse(p, varargin{:});
            
            % Store parameters
            obj.dataset = dataset;
            obj.batch_size = p.Results.batch_size;
            obj.drop_last = p.Results.drop_last;
            obj.num_workers = p.Results.num_workers;
            obj.pin_memory = p.Results.pin_memory;
            obj.worker_seed = p.Results.worker_seed;
            obj.prefetch_factor = p.Results.prefetch_factor;
            obj.collate_fn = p.Results.collate_fn;
            
            % Initialize indices
            obj.reset();
        end
        
        function reset(obj)
            % Reset the dataloader to the beginning
            
            % Get dataset length
            dataset_size = length(obj.dataset);
            
            % Create sequential indices
            obj.indices = 1:dataset_size;
            
            % Calculate number of batches
            if obj.drop_last
                obj.num_batches = floor(dataset_size / obj.batch_size);
            else
                obj.num_batches = ceil(dataset_size / obj.batch_size);
            end
            
            % Reset current index
            obj.current_idx = 1;
        end
        
        function shuffle_indices(obj)
            % Shuffle the indices for random access
            
            % Set random seed for reproducibility
            if obj.worker_seed > 0
                rng(obj.worker_seed);
            end
            
            % Shuffle indices
            obj.indices = obj.indices(randperm(length(obj.indices)));
        end
        
        function n = length(obj)
            % Return the number of batches
            % Output:
            %   n - Number of batches
            
            n = obj.num_batches;
        end
        
        function varargout = subsref(obj, s)
            % Implement indexing for the dataloader
            % Input:
            %   s - Subscript structure
            % Output:
            %   varargout - The requested output
            
            % Handle different types of indexing
            switch s(1).type
                case '()'
                    % Handle parentheses indexing: obj(idx)
                    idx = s(1).subs{1};
                    if length(s) == 1
                        % Direct indexing: obj(idx)
                        [varargout{1:nargout}] = obj.get_batch(idx);
                    else
                        % Pass remaining subscripts to the result
                        result = obj.get_batch(idx);
                        s(1) = [];
                        [varargout{1:nargout}] = subsref(result, s);
                    end
                case '.'
                    % Handle dot indexing: obj.property
                    if length(s) == 1
                        % Direct property access
                        switch s(1).subs
                            case properties(obj)
                                varargout{1} = obj.(s(1).subs);
                            case methods(obj)
                                if nargout == 0
                                    obj.(s(1).subs);
                                else
                                    varargout{1} = @(varargin) obj.(s(1).subs)(varargin{:});
                                end
                            otherwise
                                error('Unknown property or method: %s', s(1).subs);
                        end
                    else
                        % Property access followed by further indexing
                        result = obj.(s(1).subs);
                        s(1) = [];
                        [varargout{1:nargout}] = subsref(result, s);
                    end
                otherwise
                    error('Unsupported indexing type: %s', s(1).type);
            end
        end
        
        function batch = get_batch(obj, batch_idx)
            % Get a specific batch from the dataset
            % Input:
            %   batch_idx - Index of the batch to retrieve
            % Output:
            %   batch - The requested batch
            
            % Check batch index
            if batch_idx < 1 || batch_idx > obj.num_batches
                error('Batch index out of range: %d (max: %d)', batch_idx, obj.num_batches);
            end
            
            % Calculate start and end indices for this batch
            start_idx = (batch_idx - 1) * obj.batch_size + 1;
            end_idx = min(start_idx + obj.batch_size - 1, length(obj.indices));
            
            % Get batch indices
            batch_indices = obj.indices(start_idx:end_idx);
            
            % Collect samples
            samples = cell(1, length(batch_indices));
            
            % If using multiple workers, use parallel processing
            if obj.num_workers > 0
                parfor i = 1:length(batch_indices)
                    samples{i} = obj.dataset.get_item(batch_indices(i));
                end
            else
                % Sequential processing
                for i = 1:length(batch_indices)
                    samples{i} = obj.dataset.get_item(batch_indices(i));
                end
            end
            
            % Collate samples into a batch
            batch = obj.collate_fn(samples);
        end
        
        function varargout = next(obj)
            % Get the next batch and advance the iterator
            % Output:
            %   varargout - The next batch
            
            % Check if we've reached the end
            if obj.current_idx > obj.num_batches
                error('StopIteration');
            end
            
            % Get the current batch
            batch = obj.get_batch(obj.current_idx);
            
            % Advance the iterator
            obj.current_idx = obj.current_idx + 1;
            
            % Return the batch
            varargout{1} = batch;
        end
        
        function iterator = iterator(obj)
            % Return an iterator for the dataloader
            % Output:
            %   iterator - Function handle that returns the next batch
            
            % Reset the dataloader
            obj.reset();
            
            % Shuffle if needed
            if obj.shuffle
                obj.shuffle_indices();
            end
            
            % Return iterator function
            iterator = @obj.next;
        end
    end
end

function batch = default_collate(samples)
    % Default collate function to combine individual samples into a batch
    % Input:
    %   samples - Cell array of individual samples
    % Output:
    %   batch - Combined batch
    
    % For simple numeric arrays, we can just concatenate along a new dimension
    if isnumeric(samples{1})
        % Concatenate along the last dimension + 1
        batch = cat(ndims(samples{1}) + 1, samples{:});
    elseif iscell(samples{1})
        % For cell arrays, apply collate to each element
        num_elements = length(samples{1});
        batch = cell(1, num_elements);
        
        for i = 1:num_elements
            % Extract the i-th element from each sample
            elements = cellfun(@(x) x{i}, samples, 'UniformOutput', false);
            
            % Collate these elements
            batch{i} = default_collate(elements);
        end
    elseif isstruct(samples{1})
        % For structs, apply collate to each field
        fields = fieldnames(samples{1});
        batch = struct();
        
        for i = 1:length(fields)
            field = fields{i};
            
            % Extract this field from each sample
            field_values = cellfun(@(x) x.(field), samples, 'UniformOutput', false);
            
            % Collate these field values
            batch.(field) = default_collate(field_values);
        end
    else
        % For other types, just return the cell array
        batch = samples;
    end
end
