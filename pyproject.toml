[tool.pytest.ini_options]
addopts = [
  "--color=yes",
  "--durations=0",
  "--strict-markers",
  "--doctest-modules",
]
filterwarnings = [
  "ignore::DeprecationWarning",
  "ignore::UserWarning",
]
log_cli = "True"
markers = [
  "slow: slow tests",
]
minversion = "6.0"
testpaths = "tests/"

[tool.coverage.report]
exclude_lines = [
    "pragma: nocover",
    "raise NotImplementedError",
    "raise NotImplementedError()",
    "if __name__ == .__main__.:",
]
