# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
from src.utils.data_utils import NAME_TO_VAR

def nc2np(path, variables, years, save_dir, resolution, partition, partial):
    
    for year in tqdm(years):
        np_vars = {}

        # non-constant fields
        for var in variables:
            ps = glob.glob(os.path.join(f"{path}", f"{var}_{resolution}deg", partition, f"*{year}*.nc"))
            ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True)  # dataset for a single variable
            code = NAME_TO_VAR[var]

            # remove the last 24 hours if this year has 366 days
            np_vars[f"{var}"] = ds[code].to_numpy()[::6] 
            
            t, h, w = np_vars[f"{var}"].shape
            
            obs_mask = np.zeros((t, h * w))
            
            for i in range(t):
                obs_mask[i, :int(partial * h * w)]  = 1
                np.random.shuffle(obs_mask[i, :])
                
            obs_mask = np.reshape(obs_mask, (t, h, w))
        
            xr_obs_mask = xr.DataArray(
                obs_mask,
                dims=['time', 'lat', 'lon'],
                coords={
                    'time': ds["time"][::6],
                    'lat': ds.lat.values, 
                    'lon': ds.lon.values
                },
                name="mask"
            )
            
            os.makedirs(f"{save_dir}", exist_ok=True)
            
            xr_obs_mask.to_netcdf(os.path.join(save_dir, partition, f"mask_paritial{partial}_{year}.nc"))
                
@click.command()
@click.option("--root_dir", type=click.Path(exists=True))
@click.option("--save_dir", type=str)
@click.option(
    "--variables",
    "-v",
    type=click.STRING,
    multiple=True,
    default=[
        "geopotential_500",
        # "temperature_850",
    ],
)
@click.option("--resolution", type=float, default=5.625)
@click.option("--partial", type=float, default=0.2)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
def main(
    root_dir,
    save_dir,
    variables,
    resolution,
    partial,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)

    nc2np(root_dir, variables, train_years, save_dir, resolution, "train", partial)
    nc2np(root_dir, variables, val_years, save_dir, resolution, "val", partial)
    nc2np(root_dir, variables, test_years, save_dir, resolution, "test", partial)


if __name__ == "__main__":
    main()

