"""UNet-based Generator for CycleGAN Data Assimilation.

This module implements a UNet-based generator architecture specifically designed for
meteorological data assimilation tasks. It includes:
- Periodic padding to handle global atmospheric fields
- ResNet blocks for feature extraction
- Up/down-sampling paths for multi-scale feature learning
- Instance normalization for style-independent generation

Key Components:
    - UNet: Main generator architecture with skip connections
    - ResBlock: Residual blocks for deep feature extraction
    - L2Norm: L2 normalization layer for loss computation
    - Periodic padding for handling global fields

References:
    - UNet: https://arxiv.org/abs/1505.04597
    - CycleGAN: https://arxiv.org/abs/1703.10593
    - Data Assimilation: https://doi.org/10.1016/j.atmosres.2021.105901
"""

# 导入必要的库
from functools import partial  # 用于创建偏函数

import numpy as np  # 数值计算
import torch  # PyTorch深度学习框架
import torch.nn as nn  # 神经网络模块
from src.utils.model_utils import PeriodicPad2d  # 周期性填充，用于处理全球场

class L2Norm(torch.nn.Module):
    """L2范数计算层。

    计算输入张量的L2范数，用于损失函数计算。
    处理包含NaN值的数据，通过nansum忽略NaN值。
    """
    
    def __init__(self):
        """初始化L2范数层。"""
        super(L2Norm, self).__init__()

    def forward(self, x):
        """计算输入张量的L2范数。

        Args:
            x (torch.Tensor): 输入张量，形状为 [B, C, H, W]

        Returns:
            torch.Tensor: L2范数值，形状为 [B]
        """
        # 沿最后一个维度（W）计算平方和，忽略NaN值
        loss_ = torch.nansum(x**2, dim=-1)
        # 沿高度维度（H）计算和
        loss_ = torch.nansum(loss_, dim=-1)
        # 沿通道维度（C）计算和，得到每个样本的L2范数
        loss_ = torch.nansum(loss_, dim=1)

        return loss_

class ResBlock(nn.Module):
    """残差块实现。

    实现了标准的残差连接结构：
    X ------------------------identity------------------------
    |-- Convolution -- Norm -- ReLU -- Convolution -- Norm --|

    特点：
        - 使用周期性填充处理边界
        - 包含两个3x3卷积层
        - 使用实例归一化和ReLU激活
        - 可选的Dropout正则化
    """

    def __init__(self, in_channels: int, apply_dropout: bool = True):
        """初始化残差块。

        Args:
            in_channels (int): 输入通道数
            apply_dropout (bool, optional): 是否使用dropout。默认为True
        """
        super().__init__()

        # 第一个卷积块：Conv-InstanceNorm-ReLU
        conv = nn.Conv2d(in_channels=in_channels, out_channels=in_channels, kernel_size=3, stride=1)
        layers = [
            PeriodicPad2d(1),           # 周期性填充，处理全球场
            conv,                        # 3x3卷积
            nn.InstanceNorm2d(in_channels),  # 实例归一化
            nn.ReLU(True)               # ReLU激活
        ]

        # 可选的Dropout层
        if apply_dropout:
            layers += [nn.Dropout(0.5)]  # 50%的dropout率

        # 第二个卷积块：Conv-InstanceNorm
        conv = nn.Conv2d(in_channels=in_channels, out_channels=in_channels, kernel_size=3, stride=1)
        layers += [
            PeriodicPad2d(1),           # 周期性填充
            conv,                        # 3x3卷积
            nn.InstanceNorm2d(in_channels)  # 实例归一化
        ]

        # 构建顺序模型
        self.net = nn.Sequential(*layers)

    def forward(self, x): 
        """前向传播。

        Args:
            x (torch.Tensor): 输入特征图

        Returns:
            torch.Tensor: 残差连接后的输出特征图
        """
        return x + self.net(x)  # 残差连接

class UNet(nn.Module):
    """UNet生成器网络实现。

    这是一个专门为气象数据同化设计的UNet架构，具有以下特点：
    - 使用周期性填充处理全球场
    - 对称的编码器-解码器结构
    - 可配置的下采样层数和残差块数量
    - 实例归一化用于处理不同样本的统计特性

    Args:
        in_channels (int): 输入通道数，默认为1
        hidden_channels (int): 初始特征通道数，默认为64
        out_channels (int): 输出通道数，默认为1
        apply_dropout (bool): 是否在残差块中使用dropout，默认为True
        num_downsampling (int): 下采样层数，默认为2
        num_resnet_blocks (int): 残差块数量，默认为4
        init_type (str): 权重初始化方法，可选'normal'、'xavier'、'kaiming'，默认为'normal'
        init_gain (float): 初始化增益，默认为0.02

    结构示意图：
        Input
         ├── Downsample_1 ──┐
         │    ├── Downsample_2 ──┐
         │    │    ├── ResBlocks
         │    │    └── Upsample_2
         │    └── Upsample_1
         └── Output
    """

    def __init__(
        self,
        in_channels: int = 1,
        hidden_channels: int = 64,
        out_channels: int = 1,
        apply_dropout: bool = True,
        num_downsampling: int = 2,
        num_resnet_blocks: int = 4,
        init_type: str = 'normal',
        init_gain: float = 0.02,
    ):
        """初始化UNet生成器。

        Args:
            in_channels (int): 输入通道数
            hidden_channels (int): 初始特征通道数
            out_channels (int): 输出通道数
            apply_dropout (bool): 是否使用dropout
            num_downsampling (int): 下采样层数
            num_resnet_blocks (int): 残差块数量
            init_type (str): 初始化方法
            init_gain (float): 初始化增益
        """
        super().__init__()

        # 特征通道缩放因子
        f = 1
        # 保存初始化参数
        self.init_type = init_type
        self.init_gain = init_gain
        
        # 初始卷积层：7x7卷积 + 实例归一化 + ReLU
        conv = nn.Conv2d(in_channels=in_channels, out_channels=hidden_channels, kernel_size=7, stride=1)
        self.layers = [
            PeriodicPad2d(3),           # 周期性填充
            conv,                        # 7x7卷积
            nn.InstanceNorm2d(hidden_channels),  # 实例归一化
            nn.ReLU(True)               # ReLU激活
        ]

        # 下采样路径：逐步增加通道数，减小特征图尺寸
        for i in range(num_downsampling):
            conv = nn.Conv2d(hidden_channels * f, hidden_channels * 2 * f, kernel_size=3, stride=2)
            self.layers += [
                PeriodicPad2d(1),                           # 周期性填充
                conv,                                       # 3x3卷积，步长2
                nn.InstanceNorm2d(hidden_channels * 2 * f), # 实例归一化
                nn.ReLU(True)                              # ReLU激活
            ]
            f *= 2  # 加倍通道数

        # 残差块：保持特征图尺寸不变，进行深度特征提取
        for i in range(num_resnet_blocks):
            resnet_block = ResBlock(in_channels=hidden_channels * f, apply_dropout=apply_dropout)
            self.layers += [resnet_block]

        # 上采样路径：逐步减少通道数，增加特征图尺寸
        for i in range(num_downsampling):
            conv = nn.ConvTranspose2d(
                hidden_channels * f,          # 输入通道
                hidden_channels * (f//2),     # 输出通道（减半）
                kernel_size=3,                # 3x3反卷积
                stride=2,                     # 步长2用于上采样
                padding=1,                    # 填充保持尺寸
                output_padding=1              # 输出填充解决尺寸不匹配
            )
            self.layers += [
                conv,                                           # 反卷积
                nn.InstanceNorm2d(hidden_channels * (f//2)),   # 实例归一化
                nn.ReLU(True)                                  # ReLU激活
            ]
            f = f // 2  # 减半通道数

        # 输出层：7x7卷积映射到目标通道数
        conv = nn.Conv2d(in_channels=hidden_channels, out_channels=out_channels, kernel_size=7, stride=1)
        self.layers += [
            PeriodicPad2d(3),  # 周期性填充
            conv               # 7x7卷积
        ]

        # 构建完整的网络
        self.net = nn.Sequential(*self.layers)

    def init_module(self, m):
        """初始化网络模块的参数。

        支持三种初始化方式：
        - kaiming：适用于ReLU激活的层
        - xavier：适用于tanh激活的层
        - normal：标准正态分布初始化

        Args:
            m (nn.Module): 要初始化的模块
        """
        cls_name = m.__class__.__name__
        # 初始化卷积层和线性层
        if hasattr(m, 'weight') and (cls_name.find('Conv') != -1 or cls_name.find('Linear') != -1):
            # 根据指定方法初始化权重
            if self.init_type == 'kaiming':
                nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')  # He初始化
            elif self.init_type == 'xavier':
                nn.init.xavier_normal_(m.weight.data, gain=self.init_gain)  # Xavier初始化
            elif self.init_type == 'normal':
                nn.init.normal_(m.weight.data, mean=0, std=self.init_gain)  # 正态分布初始化
            else:
                raise ValueError('不支持的初始化方法！')

            # 偏置初始化为0
            if m.bias is not None:
                nn.init.constant_(m.bias.data, val=0)

        # 初始化BatchNorm层
        if hasattr(m, 'weight') and cls_name.find('BatchNorm2d') != -1:
            nn.init.normal_(m.weight.data, mean=1.0, std=self.init_gain)  # 权重初始化
            nn.init.constant_(m.bias.data, val=0)                         # 偏置初始化为0

    def forward(self, x):
        """模型前向传播。

        将输入气象场通过UNet结构进行转换，生成目标场。整个过程包括：
        1. 下采样路径：逐步提取深层特征
        2. 残差块：进行特征转换
        3. 上采样路径：重建目标场

        Args:
            x (torch.Tensor): 输入气象场，形状为 [B, C, H, W]
                - B: 批次大小
                - C: 输入通道数
                - H: 高度
                - W: 宽度

        Returns:
            torch.Tensor: 生成的目标场，形状为 [B, C_out, H, W]
                - C_out: 输出通道数
        """
        # 通过完整的UNet网络
        preds = self.net(x)

        return preds
