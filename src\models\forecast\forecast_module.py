"""气象预报深度学习模块。

此模块实现了一个基于PyTorch Lightning的深度学习框架，专门用于气象预报任务。
它继承自LightningModule，提供了完整的训练、验证和测试流程。

主要特点：
1. 模型架构：
   - 支持各种深度学习模型，如AFNONet等
   - 灵活的网络结构，可适应不同的预报任务

2. 损失函数：
   - 支持多种损失函数，适用于气象预报的特殊需求
   - 可自定义损失函数组合

3. 评估指标：
   - RMSE（均方根误差）：评估预报与真实值的偏差
   - ACC（异常相关系数）：评估预报与真实值的相关性
   - 支持全球加权评估，考虑不同纬度的面积权重

4. 优化策略：
   - 支持多种优化器和学习率调度器
   - 针对位置编码等特殊参数的差异化权重衰减

技术细节：
1. 数据处理：
   - 支持气候态数据的标准化
   - 处理不同分辨率的气象场

2. 训练技巧：
   - 学习率自适应调整
   - 早停策略避免过拟合

参考文献：
1. AFNONet: https://arxiv.org/abs/2111.13587
2. FourCastNet: https://arxiv.org/abs/2202.11214
3. 气象预报: https://doi.org/10.1175/BAMS-D-20-0317.1
"""

# 导入必要的库
from typing import Any, Dict, Tuple  # 类型提示，用于增强代码可读性和IDE支持
import copy  # 用于对象的深拷贝和浅拷贝
import torch  # PyTorch深度学习框架
import pickle  # Python对象序列化和反序列化
import numpy as np  # 科学计算库
from pytorch_lightning import LightningModule  # Lightning训练框架
from torchmetrics import MinMetric, MeanMetric, MaxMetric  # 指标计算和追踪
from src.utils.weighted_acc_rmse import weighted_rmse_torch, weighted_acc_torch  # 自定义评估指标

class ForecastLitModule(LightningModule):
    """气象预报深度学习模块。
    
    此类实现了一个完整的气象预报系统，包括：
    1. 模型架构：支持各种深度学习模型进行气象预报
    2. 训练流程：完整的训练、验证和测试流程
    3. 评估系统：多重评估指标的计算和记录
    4. 数据管理：气候数据处理和标准化
    
    Attributes:
        net (nn.Module): 预报模型
        criterion: 损失函数
        train_loss: 训练损失计算器
        val_loss: 验证损失计算器
        test_loss: 测试损失计算器
        mult: 用于RMSE计算的标准差
        clims: 气候态数据列表
        val_loss_best: 最佳验证损失记录器
        val_rmse_best: 最佳验证RMSE记录器
        val_acc_best: 最佳验证ACC记录器
    """

    def __init__(
        self,
        net: torch.nn.Module,  # 预报模型
        optimizer: torch.optim.Optimizer,  # 优化器
        scheduler: torch.optim.lr_scheduler,  # 学习率调度器
        after_scheduler: torch.optim.lr_scheduler,  # 后续学习率调度器
        mean_path: str,  # 均值文件路径
        std_path: str,  # 标准差文件路径
        clim_paths: list,  # 气候态数据文件路径列表
        loss: object,  # 损失函数
    ) -> None:
        """初始化气象预报模块。

        Args:
            net: 用于预报的深度学习模型
            optimizer: 训练使用的优化器
            scheduler: 主要学习率调度器
            after_scheduler: 后续学习率调度器
            mean_path: 均值文件路径，用于数据标准化
            std_path: 标准差文件路径，用于数据标准化
            clim_paths: 气候态数据文件路径列表，用于计算异常场
            loss: 损失函数对象
        """
        super().__init__()

        # 保存超参数以便后续访问，同时确保这些参数会被存储在checkpoint中
        self.save_hyperparameters(logger=False)

        # 设置预报模型
        self.net = net

        # 设置损失函数
        self.criterion = self.hparams.loss

        # 初始化用于记录平均损失的指标
        self.train_loss = MeanMetric()  # 训练损失平均值
        self.val_loss = MeanMetric()    # 验证损失平均值
        self.test_loss = MeanMetric()   # 测试损失平均值

        # 加载均值和标准差数据，用于数据标准化和反标准化
        self.mean = np.load(mean_path)  # 加载均值
        self.std = np.load(std_path)    # 加载标准差
        
        # 将标准差转换为tensor，用于后续计算
        self.mult = torch.tensor(self.std, dtype=torch.float32, requires_grad=False)
        
        # 加载并处理气候态数据，用于计算异常场
        self.clims = []
        for i in range(len(clim_paths)):
            clim_raw = np.load(clim_paths[i])  # 加载原始气候态数据
            clim_np = np.ones([1, 1, self.net.img_size[0], self.net.img_size[1]])  # 创建形状匹配的数组
            clim_np = ((clim_raw - self.mean) / self.std) * clim_np  # 标准化气候态数据
            self.clims.append(torch.tensor(clim_np, dtype=torch.float32, requires_grad=False))  # 转换为tensor并添加到列表

        # 初始化用于记录最佳验证指标的指标
        self.val_loss_best = MinMetric()  # 最小验证损失
        self.val_rmse_best = MinMetric()  # 最小验证RMSE
        self.val_acc_best = MinMetric()   # 最小验证ACC（注意：通常ACC越大越好，但这里使用MinMetric是因为在代码中取了负值）

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """执行模型前向传播。

        将输入数据传递给模型，获取预报结果。

        Args:
            x: 输入张量，通常是初始气象场

        Returns:
            预报结果张量
        """
        return self.net(x)

    def on_train_start(self) -> None:
        """训练开始时的钩子函数。

        Lightning在训练开始前会执行验证步骤的完整性检查，
        因此需要确保验证指标不会存储这些检查的结果。
        """
        # 重置所有验证指标
        self.val_loss.reset()          # 重置验证损失
        self.val_loss_best.reset()     # 重置最佳验证损失
        self.val_rmse_best.reset()     # 重置最佳验证RMSE
        self.val_acc_best.reset()      # 重置最佳验证ACC
        
    def model_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], phase: str
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """执行单步模型计算。

        对一批数据执行前向传播、损失计算等操作。

        Args:
            batch: 数据批次，包含输入张量和目标张量
            phase: 阶段标识，可以是'train'、'val'或'test'

        Returns:
            包含损失、预测结果和目标值的元组
        """
        x, y1 = batch  # 解包数据批次，x是输入，y1是目标
        preds = self.forward(x.to(self.device))  # 执行前向传播，获取预测结果
        
        # 在验证和测试阶段，分离计算图以节省内存
        if (phase == 'val') or (phase == 'test'):
            preds = preds.detach()
            
        # 计算损失
        loss = self.criterion(preds, y1)
        
        # 返回损失、预测结果和目标值
        return loss, preds.detach(), y1

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> torch.Tensor:
        """执行单步训练。

        对一批训练数据执行前向传播、损失计算和反向传播。

        Args:
            batch: 训练数据批次，包含输入张量和目标张量
            batch_idx: 批次索引

        Returns:
            训练损失，用于反向传播
        """
        # 调用model_step获取损失、预测结果和目标值
        loss, preds, targets = self.model_step(batch, "train")

        # 更新并记录训练损失
        self.train_loss(loss)
        self.log("train/loss", self.train_loss, on_step=False, on_epoch=True, prog_bar=True)

        # 返回损失用于反向传播
        return loss

    def on_train_epoch_end(self) -> None:
        """训练周期结束时的钩子函数。

        可以在此处执行训练周期结束后的操作，如打印统计信息等。
        当前实现为空，可根据需要添加具体逻辑。
        """
        pass

    def validation_step(self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> Dict:
        """执行单步验证。

        对一批验证数据执行前向传播和评估指标计算。

        Args:
            batch: 验证数据批次，包含输入张量和目标张量
            batch_idx: 批次索引

        Returns:
            包含验证指标的字典
        """
        # 调用model_step获取损失、预测结果和目标值
        loss, preds, targets = self.model_step(batch, "val")
        
        # 计算RMSE（均方根误差）
        val_rmse = self.mult.to(self.device, dtype=preds.dtype) * weighted_rmse_torch(preds, targets)
        val_rmse = val_rmse.detach()
        
        # 计算ACC（异常相关系数）
        val_acc = weighted_acc_torch(preds - self.clims[1].to(self.device, dtype=preds.dtype), 
                                     targets - self.clims[1].to(self.device, dtype=preds.dtype))
        val_acc = val_acc.detach()

        # 更新并记录验证指标
        self.val_loss(loss)
        self.log("val/loss", self.val_loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val/rmse", val_rmse, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val/acc", val_acc, on_step=False, on_epoch=True, prog_bar=True)
        
        # 返回验证指标字典
        return {'rmse': val_rmse, 'acc': val_acc, 'preds': preds, 'targets': targets}

    def validation_epoch_end(self, validation_step_outputs) -> None:
        """验证周期结束时的处理函数。

        计算整个验证集的平均指标，并更新最佳记录。

        Args:
            validation_step_outputs: 所有验证步骤的输出列表
        """
        # 初始化累加器
        val_rmse, val_acc = 0, 0
        
        # 计算平均RMSE和ACC
        for out in validation_step_outputs:
            val_rmse += out['rmse'] / len(validation_step_outputs)
            val_acc += out['acc'] / len(validation_step_outputs)

        # 获取当前验证损失
        loss = self.val_loss.compute()
        
        # 更新最佳记录
        self.val_loss_best(loss)       # 更新最佳验证损失
        self.val_rmse_best(val_rmse)   # 更新最佳验证RMSE
        self.val_acc_best(val_acc)     # 更新最佳验证ACC

        # 记录最佳指标
        self.log("val/loss_best", self.val_loss_best.compute(), prog_bar=True)
        self.log("val/rmse_best", self.val_rmse_best.compute(), prog_bar=True)
        self.log("val/acc_best", self.val_acc_best.compute(), prog_bar=True)

    def on_validation_epoch_end(self) -> None:
        """验证周期结束时的钩子函数。

        可以在此处执行验证周期结束后的操作。
        当前实现为空，可根据需要添加具体逻辑。
        """
        pass

    def test_step(self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> None:
        """执行单步测试。

        对一批测试数据执行前向传播和评估指标计算。

        Args:
            batch: 测试数据批次，包含输入张量和目标张量
            batch_idx: 批次索引
        """
        # 使用torch.inference_mode(False)允许在测试时计算梯度，这对某些需要梯度的操作很重要
        with torch.inference_mode(False):
            # 调用model_step获取损失、预测结果和目标值
            loss, preds, targets = self.model_step(batch, "test")
            
            # 计算RMSE（均方根误差）
            test_rmse = self.mult.to(self.device, dtype=preds.dtype) * weighted_rmse_torch(preds, targets)
            test_rmse = test_rmse.detach()
            
            # 计算ACC（异常相关系数）
            test_acc = weighted_acc_torch(preds - self.clims[2].to(self.device, dtype=preds.dtype), 
                                        targets - self.clims[2].to(self.device, dtype=preds.dtype))
            test_acc = test_acc.detach()

            # 更新并记录测试指标
            self.test_loss(loss)
            self.log("test/loss", self.test_loss, on_step=False, on_epoch=True, prog_bar=True)
            self.log("test/rmse", test_rmse, on_step=False, on_epoch=True, prog_bar=True)
            self.log("test/acc", test_acc, on_step=False, on_epoch=True, prog_bar=True)
        
    def on_test_epoch_end(self) -> None:
        """测试周期结束时的钩子函数。

        可以在此处执行测试周期结束后的操作，如保存结果等。
        当前实现为空，可根据需要添加具体逻辑。
        """
        pass

    def configure_optimizers(self) -> Dict[str, Any]:
        """配置优化器和学习率调度器。

        根据模型参数的不同特性，可以为不同参数设置不同的优化策略。
        例如，位置编码通常不需要权重衰减。

        Returns:
            包含优化器和学习率调度器配置的字典
        """
        # 将参数分为需要权重衰减和不需要权重衰减的两组
        decay = []      # 需要权重衰减的参数
        no_decay = []   # 不需要权重衰减的参数
        
        # 遍历所有命名参数
        for name, m in self.named_parameters():
            # 位置编码参数不需要权重衰减
            if "pos_embed" in name:
                no_decay.append(m)
            # 其他参数需要权重衰减
            else:
                decay.append(m)
        
        # 配置优化器，为不同参数组设置不同的优化选项
        optimizer = self.hparams.optimizer(params=self.parameters([
            {
                "params": decay,  # 需要权重衰减的参数组
            },
            {
                "params": no_decay,  # 不需要权重衰减的参数组
                "weight_decay": 0,   # 设置权重衰减为0
            }
        ]))
        
        # 如果提供了学习率调度器，则配置调度器
        if self.hparams.scheduler is not None:
            # 如果提供了后续调度器，则配置级联调度器
            if self.hparams.after_scheduler is not None:
                after_scheduler = self.hparams.after_scheduler(optimizer=optimizer)
                scheduler = self.hparams.scheduler(optimizer=optimizer, after_scheduler=after_scheduler)
            # 否则只配置主调度器
            else:
                scheduler = self.hparams.scheduler(optimizer=optimizer)
            
            # 返回包含优化器和学习率调度器的配置
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "monitor": "val/loss",  # 监控验证损失来调整学习率
                    "interval": "epoch",    # 按周期调整学习率
                    "frequency": 1,         # 每个周期调整一次
                },
            }
        
        # 如果没有提供学习率调度器，则只返回优化器
        return {"optimizer": optimizer}


# 模块测试代码
if __name__ == "__main__":
    _ = ForecastLitModule(None, None, None, None)