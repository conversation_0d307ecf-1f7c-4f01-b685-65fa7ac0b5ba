{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Using MoXing-v2.0.1.rc0.ffd1c0c8-ffd1c0c8\n", "INFO:root:Using OBS-Python-SDK-********\n"]}], "source": ["import moxing as mox\n", "mox.file.copy(\"s3:///urchincache/attachment/5/d/5d2ee00f-2ce0-4053-93e5-5a63e586a984/np_era5_6hourly_debug.zip\", \"/cache/data/data.zip\") "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Archive:  /cache/data/data.zip\n", "   creating: /cache/data/np_era5_6hourly_debug/\n", "  inflating: /cache/data/np_era5_6hourly_debug/lat.npy  \n", "  inflating: /cache/data/np_era5_6hourly_debug/lon.npy  \n", "  inflating: /cache/data/np_era5_6hourly_debug/normalize_mean.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/normalize_std.npz  \n", "   creating: /cache/data/np_era5_6hourly_debug/test/\n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2021_0.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2021_1.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2021_2.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2021_3.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2022_0.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2022_1.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2022_2.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/2022_3.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/test/climatology.npz  \n", "   creating: /cache/data/np_era5_6hourly_debug/train/\n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2018_0.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2018_1.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2018_2.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2018_3.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2019_0.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2019_1.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2019_2.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/2019_3.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/train/climatology.npz  \n", "   creating: /cache/data/np_era5_6hourly_debug/val/\n", "  inflating: /cache/data/np_era5_6hourly_debug/val/2020_0.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/val/2020_1.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/val/2020_2.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/val/2020_3.npz  \n", "  inflating: /cache/data/np_era5_6hourly_debug/val/climatology.npz  \n"]}], "source": ["!unzip -d /cache/data /cache/data/data.zip"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PyTorch-1.11", "language": "python", "name": "pytorch-1.11"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.10"}}, "nbformat": 4, "nbformat_minor": 4}