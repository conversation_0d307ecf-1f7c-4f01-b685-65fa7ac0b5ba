"""基于CycleGAN的气象数据同化模块。

此模块实现了一个基于CycleGAN的深度学习框架，专门用于气象数据同化任务。
它继承自PyTorch Lightning的LightningModule，提供了完整的训练、验证和测试流程。

主要特点：
1. 双向映射：
   - 背景场 -> 分析场：通过生成器G_A2B实现
   - 分析场 -> 背景场：通过生成器G_B2A实现

2. 多重损失函数：
   - 对抗损失：使用PatchGAN判别器
   - 循环一致性损失：确保双向转换的一致性
   - 身份映射损失：保持相同域内的特征
   - 重建损失：保证生成结果的准确性

3. 评估指标：
   - RMSE（均方根误差）
   - ACC（异常相关系数）
   - SSIM（结构相似性）

4. 优化策略：
   - 分别优化生成器和判别器
   - 使用图像池存储历史生成样本
   - 动态学习率调整

技术细节：
1. 网络架构：
   - 生成器：使用U-Net架构，适合气象场的特征提取和重建
   - 判别器：使用PatchGAN，关注局部特征的真实性

2. 训练技巧：
   - 使用实例归一化处理不同样本的统计特性
   - 采用周期性填充处理全球场
   - 通过掩码处理缺失观测

3. 数据处理：
   - 支持气候态数据的标准化
   - 处理观测数据的掩码操作
   - 计算全球加权评估指标

参考文献：
1. CycleGAN: https://arxiv.org/abs/1703.10593
2. U-Net: https://arxiv.org/abs/1505.04597
3. PatchGAN: https://arxiv.org/abs/1611.07004
4. 数据同化: https://doi.org/10.1016/j.atmosres.2021.105901
"""

# 导入必要的库
from typing import Any, Dict, Tuple  # 类型提示
import itertools  # 用于高效处理迭代器
import copy  # 对象的深拷贝和浅拷贝
import torch  # PyTorch深度学习框架
import pickle  # 序列化和反序列化
import numpy as np  # 科学计算
from pytorch_lightning import LightningModule  # Lightning训练框架
import torchvision  # 图像处理
from torchmetrics import MinMetric, MeanMetric, MaxMetric  # 指标计算
from torchmetrics import StructuralSimilarityIndexMeasure as SSIM  # 结构相似性
from src.utils.weighted_acc_rmse import weighted_rmse_torch, weighted_acc_torch  # 评估指标
from src.utils.train_utils import ImagePool  # 图像池管理

class AssimilateLitModule(LightningModule):
    """基于CycleGAN的气象数据同化模块。

    此类实现了一个完整的数据同化系统，包括：
    1. 模型架构：双生成器（G_A2B, G_B2A）和双判别器（D_A, D_B）
    2. 训练流程：交替优化生成器和判别器
    3. 评估系统：多重评估指标的计算和记录
    4. 数据管理：图像池维护和气候数据处理

    Attributes:
        g_A2B (nn.Module): 背景场到分析场的生成器
        g_B2A (nn.Module): 分析场到背景场的生成器
        d_A (nn.Module): 背景场判别器
        d_B (nn.Module): 分析场判别器
        criterion: 损失函数集合
        fake_pool_A: 背景场图像池
        fake_pool_B: 分析场图像池
        ssim: 结构相似性计算器
        mult: 用于RMSE计算的标准差
        clims: 气候态数据列表

    Args:
        g_A2B (nn.Module): 背景场到分析场的生成器
        g_B2A (nn.Module): 分析场到背景场的生成器
        d_A (nn.Module): 背景场判别器
        d_B (nn.Module): 分析场判别器
        g_optimizer: 生成器优化器配置
        d_optimizer: 判别器优化器配置
        scheduler: 主要学习率调度器
        after_scheduler: 次要学习率调度器
        mean_path (str): 均值文件路径
        std_path (str): 标准差文件路径
        clim_paths (list): 气候态数据文件路径列表
        loss: 损失函数配置
    """

    def __init__(
        self,
        g_A2B: torch.nn.Module,  # 从域A到域B的生成器
        g_B2A: torch.nn.Module,  # 从域B到域A的生成器
        d_A: torch.nn.Module,    # 域A的判别器
        d_B: torch.nn.Module,    # 域B的判别器
        g_optimizer: torch.optim.Optimizer,  # 生成器优化器
        d_optimizer: torch.optim.Optimizer,  # 判别器优化器
        scheduler: torch.optim.lr_scheduler,  # 学习率调度器
        after_scheduler: torch.optim.lr_scheduler,  # 后续学习率调度器
        mean_path: str,  # 均值文件路径
        std_path: str,   # 标准差文件路径
        clim_paths: list,  # 气候数据文件路径列表
        loss: object,    # 损失函数
    ) -> None:
        """Initialize a `FourCastNetLitModule`.

        :param net: The model to train.
        :param optimizer: The optimizer to use for training.
        :param scheduler: The learning rate scheduler to use for training.
        """
        super().__init__()

        # 保存超参数以便后续访问，同时确保这些参数会被存储在checkpoint中
        self.save_hyperparameters(logger=False)

        # 初始化生成器和判别器
        self.g_A2B = g_A2B
        self.g_B2A = g_B2A
        self.d_A = d_A
        self.d_B = d_B
        
        # 初始化SSIM（结构相似性）指标及其最佳值记录
        self.ssim = SSIM()
        self.ssim_best = MaxMetric()

        # 初始化损失函数
        self.criterion = self.hparams.loss
        
        # 获取判别器和生成器的参数
        self.d_A_params = self.d_A.parameters()
        self.d_B_params = self.d_B.parameters()
        self.g_params = itertools.chain([*self.g_A2B.parameters(), *self.g_B2A.parameters()])

        # 初始化图像池，用于存储生成的假图像
        self.fake_pool_A = ImagePool(pool_sz=50)
        self.fake_pool_B = ImagePool(pool_sz=50)
        self.train_bias_buffer = []  # 训练偏差缓冲区
        self.val_bias_buffer = []    # 验证偏差缓冲区
        
        # 初始化用于记录平均损失的指标
        self.train_loss = MeanMetric()
        self.val_loss = MeanMetric()
        self.test_loss = MeanMetric()

        # 加载均值和标准差数据
        self.mean = np.load(mean_path)
        self.std = np.load(std_path)
        
        # 将标准差转换为tensor，用于后续计算
        self.mult = torch.tensor(self.std, dtype=torch.float32, requires_grad=False)
        
        # 加载并处理气候数据
        self.clims = []
        for i in range(len(clim_paths)):
            clim_raw = np.load(clim_paths[i])
            clim_np = np.ones([1, 1, 32, 64])
            clim_np = ((clim_raw - self.mean) / self.std) * clim_np
            self.clims.append(torch.tensor(clim_np, dtype=torch.float32, requires_grad=False))

        # 初始化用于记录最佳验证指标的指标
        self.val_loss_best = MinMetric()
        self.val_rmse_best = MinMetric()
        self.val_acc_best = MaxMetric()

    @staticmethod
    def set_requires_grad(nets, requires_grad=False):
        """
        设置网络中所有参数是否需要计算梯度，以避免不必要的计算
        参数:
            nets (network list)   -- 网络列表
            requires_grad (bool)  -- 是否需要计算梯度
        """

        # 如果nets不是列表，则将其转换为列表
        if not isinstance(nets, list): nets = [nets]
        # 遍历网络列表中的每个网络
        for net in nets:
            # 遍历网络中的每个参数
            for param in net.parameters():
                # 设置参数是否需要计算梯度
                param.requires_grad = requires_grad

    def forward(self, xb: torch.Tensor, obs: torch.Tensor, obs_mask: torch.Tensor, xt: torch.Tensor) -> torch.Tensor:
        """
            前向传播函数，用于模型的推理阶段。与训练步骤不同，此函数应被视为最终的推理代码。
            你可以在训练步骤中复用此代码以提高代码的可重用性。
            参数:
                xb -- 域A的真实图像
                obs -- 观测数据
                obs_mask -- 观测数据的掩码
                xt -- 域B的真实图像
        """
        # 使用生成器g_A2B生成域B的假图像
        fake_B = self.g_A2B(xb, obs, obs_mask)
        # 使用生成器g_B2A生成域A的假图像
        fake_A = self.g_B2A(xt)
        
        # 返回生成的假图像
        return fake_B, fake_A 

    def on_train_start(self) -> None:
        """Lightning hook that is called when training begins."""
        # 默认情况下，Lightning会在训练开始前执行验证步骤的完整性检查
        # 因此需要确保验证指标不会存储这些检查的结果
        self.val_loss.reset()          # 重置验证损失
        self.val_loss_best.reset()     # 重置最佳验证损失
        self.val_rmse_best.reset()     # 重置最佳验证RMSE
        self.val_acc_best.reset()      # 重置最佳验证准确率
        self.ssim_best.reset()         # 重置最佳SSIM
        self.ssim.reset()              # 重置当前SSIM
    
    def forward_gen(self, xb, obs, obs_mask, xt, fake_A, fake_B):
        """
        获取生成器在训练/验证步骤中的剩余输出
        参数:
            xb -- 域A的真实图像
            xt -- 域B的真实图像
            fake_A -- 域A的假图像
            fake_B -- 域B的假图像
        """
        # 使用生成器g_B2A生成域A的循环一致性图像
        cyc_A = self.g_B2A(fake_B)
        # 使用生成器g_B2A生成域A的恒等映射图像
        idt_A = self.g_B2A(xb)

        # 使用生成器g_A2B生成域B的循环一致性图像
        cyc_B = self.g_A2B(fake_A, obs, obs_mask)
        # 使用生成器g_A2B生成域B的恒等映射图像
        idt_B = self.g_A2B(xt, obs, obs_mask)

        # 返回生成的图像
        return cyc_A, idt_A, cyc_B, idt_B
    
    @staticmethod
    def forward_dis_A(dis, real_data, fake_data):
        """
        获取判别器A的输出
        
        Args:
            dis (torch.nn.Module): 判别器A，用于区分真实图像和假图像。
            real_data (torch.Tensor): 真实图像数据，其形状通常为 (batch_size, channels, height, width)。
            fake_data (torch.Tensor): 假图像数据，其形状与real_data相同。
        
        Returns:
            tuple: 包含两个元素，分别是对真实图像和假图像的预测结果，均为形状为 (batch_size,) 的Tensor。
        """
        
        # 判别器对真实图像的预测
        pred_real_data = dis(real_data)
        # 判别器对假图像的预测
        pred_fake_data = dis(fake_data)

        # 返回判别器对真实图像和假图像的预测结果
        return pred_real_data, pred_fake_data

        pred_real_data = dis(real_data)
        pred_fake_data = dis(fake_data)

        return pred_real_data, pred_fake_data
    
    @staticmethod
    def forward_dis_B(dis, real_data, fake_data, obs, obs_mask):
        """
        获取判别器B的输出
        
        Args:
            dis (torch.nn.Module): 判别器B，用于区分真实图像和假图像。
            real_data (torch.Tensor): 真实图像数据，其形状通常为 (batch_size, channels, height, width)。
            fake_data (torch.Tensor): 假图像数据，其形状与real_data相同。
            obs (torch.Tensor): 观测数据，用于与图像数据拼接。
            obs_mask (torch.Tensor): 观测数据的掩码，用于过滤无效观测。
        
        Returns:
            tuple: 包含两个元素，分别是对真实图像和假图像的预测结果，均为形状为 (batch_size,) 的Tensor。
        """
        # 将真实图像与观测数据拼接后输入判别器
        pred_real_data = dis(torch.concat([real_data, obs * obs_mask], dim=1))
        # 将假图像与观测数据拼接后输入判别器
        pred_fake_data = dis(torch.concat([fake_data, obs * obs_mask], dim=1))

        # 返回判别器对真实图像和假图像的预测结果
        return pred_real_data, pred_fake_data

        return pred_real_data, pred_fake_data
     
    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int, optimizer_idx: int
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        执行单步训练，根据optimizer_idx的不同，分别处理生成器和判别器的训练
        
        参数:
            batch (Tuple[torch.Tensor, torch.Tensor]): 包含输入图像和目标标签的数据批次
            batch_idx (int): 当前批次的索引
            optimizer_idx (int): 优化器的索引，用于区分生成器和判别器的训练
            
        返回:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: 包含损失、预测和目标标签的元组
        """
        # 解包批次数据
        xb, obs, obs_mask, xt = batch
        # 使用生成器生成假图像
        fake_B, fake_A = self(xb, obs, obs_mask, xt)
        
        # 处理生成器的训练
        if optimizer_idx == 0:
            # 获取生成器的剩余输出
            cyc_A, idt_A, cyc_B, idt_B = self.forward_gen(xb, obs, obs_mask, xt, fake_A, fake_B)
            
            # 设置判别器参数不需要计算梯度
            self.set_requires_grad([self.d_A, self.d_B], requires_grad=False)
            # 获取判别器对假图像的预测
            d_A_pred_fake_data = self.d_A(fake_A)
            d_B_pred_fake_data = self.d_B(torch.concat([fake_B, obs * obs_mask], dim=1))
            
            # 计算生成器的损失
            g_A2B_loss, g_B2A_loss, g_tot_loss = self.criterion.get_gen_loss(xb, xt, cyc_A, cyc_B, idt_A, idt_B,
                                                                        d_A_pred_fake_data, d_B_pred_fake_data)
            # 计算重建损失
            rec_A2B_loss = self.criterion.reconstruct_loss(fake_B, xt)
             
            # 记录生成器的损失
            dict_ = {'g_tot_train_loss': g_tot_loss,
                     'g_A2B_train_loss': g_A2B_loss,
                     'g_B2A_train_loss': g_B2A_loss,
                     'g_A2B_rec_train_loss': rec_A2B_loss,
                     }

            self.log_dict(dict_, on_step=True,
                          on_epoch=True,
                          prog_bar=True,
                          logger=True)
            
            # 返回总损失
            return g_tot_loss + rec_A2B_loss
        
        # 处理判别器A的训练
        if optimizer_idx == 1:
            # 设置判别器A参数需要计算梯度
            self.set_requires_grad([self.d_A], requires_grad=True)
            # 从图像池中获取假图像
            fake_A = self.fake_pool_A.push_and_pop(fake_A)
            # 获取判别器A对真实图像和假图像的预测
            d_A_pred_real_data, d_A_pred_fake_data = self.forward_dis_A(self.d_A, xb, fake_A.detach())

            # 计算判别器A的损失
            d_A_loss = self.criterion.get_dis_loss(d_A_pred_real_data, d_A_pred_fake_data)
            
            # 记录判别器A的损失
            self.log("d_A_train_loss", d_A_loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)

            # 返回判别器A的损失
            return d_A_loss
            
        # 处理判别器B的训练
        if optimizer_idx == 2:
            # 设置判别器B参数需要计算梯度
            self.set_requires_grad([self.d_B], requires_grad=True)
            # 从图像池中获取假图像
            fake_B = self.fake_pool_B.push_and_pop(fake_B)
            # 获取判别器B对真实图像和假图像的预测
            d_B_pred_real_data, d_B_pred_fake_data = self.forward_dis_B(self.d_B, xt, fake_B.detach(), obs, obs_mask)

            # 计算判别器B的损失
            d_B_loss = self.criterion.get_dis_loss(d_B_pred_real_data, d_B_pred_fake_data)
            # 计算重建损失
            rec_B_loss = self.criterion.rec_loss(fake_B, xt)
            # 记录判别器B的损失
            self.log("d_B_train_loss", d_B_loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)
            self.log("rec_B_train_loss", rec_B_loss, on_step=True, on_epoch=True, prog_bar=True, logger=True)

            # 返回判别器B的总损失
            return d_B_loss + rec_B_loss

        
    def shared_step(self, batch, stage: str = 'val'):
        """
        共享步骤，用于训练、验证和测试阶段，计算损失和指标
        
        参数:
            batch (Tuple[torch.Tensor, torch.Tensor]): 包含输入图像和目标标签的数据批次
            stage (str): 当前阶段，可以是 'train', 'val' 或 'test'
            
        返回:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: 包含总损失、预测图像和目标图像的元组
        """
        # 解包批次数据
        xb, obs, obs_mask, xt = batch
        # 使用生成器生成假图像
        fake_B, fake_A = self(xb, obs, obs_mask, xt)

        # 计算RMSE（均方根误差）
        rmse = self.mult.to(self.device, dtype=fake_B.dtype) * weighted_rmse_torch(fake_B, xt)
        rmse = rmse.detach()
        # 计算ACC（准确率）
        acc = weighted_acc_torch(fake_B - self.clims[1].to(self.device, dtype=fake_B.dtype), 
                                     xt - self.clims[1].to(self.device, dtype=xt.dtype))
        acc = acc.detach()

        # 获取生成器的剩余输出
        cyc_A, idt_A, cyc_B, idt_B = self.forward_gen(xb, obs, obs_mask, xt, fake_A, fake_B)

        # 获取判别器A和B的输出
        d_A_pred_real_data, d_A_pred_fake_data = self.forward_dis_A(self.d_A, xb, fake_A)
        d_B_pred_real_data, d_B_pred_fake_data = self.forward_dis_B(self.d_B, xt, fake_B, obs, obs_mask)

        # 计算生成器的损失
        g_A2B_loss, g_B2A_loss, g_tot_loss = self.criterion.get_gen_loss(xb, xt, cyc_A, cyc_B, idt_A, idt_B,
                                                                    d_A_pred_fake_data, d_B_pred_fake_data)

        # 计算判别器A和B的损失
        d_A_loss = self.criterion.get_dis_loss(d_A_pred_real_data, d_A_pred_fake_data)
        d_B_loss = self.criterion.get_dis_loss(d_B_pred_real_data, d_B_pred_fake_data)

        # 更新SSIM（结构相似性）指标
        self.ssim.update(fake_B, xt.to(fake_B.dtype))

        # 记录所有指标
        dict_ = {f'{stage}/ssim': self.ssim,
                f'{stage}/rmse': rmse,
                f'{stage}/acc': acc,
                f'g_tot_{stage}_loss': g_tot_loss,
                f'g_A2B_{stage}_loss': g_A2B_loss,
                f'g_B2A_{stage}_loss': g_B2A_loss,
                f'd_A_{stage}_loss': d_A_loss,
                f'd_B_{stage}_loss': d_B_loss}

        # 记录指标到日志
        self.log_dict(dict_, on_step=False, on_epoch=True, prog_bar=True, logger=True)

        # 返回总损失、预测图像和目标图像
        return g_tot_loss, fake_B.detach(), xt

    def validation_step(self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> None:
        """
        执行单步验证，计算并记录验证集的损失和指标
        
        参数:
            batch (Tuple[torch.Tensor, torch.Tensor]): 包含输入图像和目标标签的数据批次
            batch_idx (int): 当前批次的索引
            
        返回:
            dict: 包含验证集的RMSE、ACC、预测图像和目标图像的字典
        """
        # 调用共享步骤，获取损失、预测图像和目标图像
        loss, preds, targets = self.shared_step(batch, "val")
        
        # 计算验证集的RMSE（均方根误差）
        val_rmse = self.mult.to(self.device, dtype=preds.dtype) * weighted_rmse_torch(preds, targets)
        val_rmse = val_rmse.detach()
        
        # 计算验证集的ACC（准确率）
        val_acc = weighted_acc_torch(preds - self.clims[1].to(self.device, dtype=preds.dtype), 
                                     targets - self.clims[1].to(self.device, dtype=preds.dtype))
        val_acc = val_acc.detach()

        # 更新并记录验证集的损失和指标
        self.val_loss(loss)
        self.log("val/loss", self.val_loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val/rmse", val_rmse, on_step=False, on_epoch=True, prog_bar=True)
        self.log("val/acc", val_acc, on_step=False, on_epoch=True, prog_bar=True)
        
        # 返回验证集的RMSE、ACC、预测图像和目标图像
        return {'rmse': val_rmse, 'acc': val_acc, 'preds': preds, 'targets': targets}

    def validation_epoch_end(self, validation_step_outputs) -> None:
        """
        在验证周期结束时调用，计算并记录整个验证周期的平均指标
        
        参数:
            validation_step_outputs (list): 包含每个验证步骤输出的列表
        """
        # 初始化RMSE和ACC的累加器
        val_rmse, val_acc = 0, 0
        # 遍历每个验证步骤的输出，计算平均RMSE和ACC
        for out in validation_step_outputs:
            val_rmse += out['rmse'] / len(validation_step_outputs)
            val_acc += out['acc'] / len(validation_step_outputs)

        # 计算当前验证周期的总损失
        loss = self.val_loss.compute()  # 获取当前验证损失
        # 更新最佳验证损失
        self.val_loss_best(loss)  # 更新迄今为止的最佳验证损失
        # 更新最佳验证RMSE
        self.val_rmse_best(val_rmse)  # 更新迄今为止的最佳验证RMSE
        # 更新最佳验证ACC
        self.val_acc_best(val_acc)  # 更新迄今为止的最佳验证ACC

        # 记录最佳验证指标到日志
        self.log("val/loss_best", self.val_loss_best.compute(), prog_bar=True)
        self.log("val/rmse_best", self.val_rmse_best.compute(), prog_bar=True)
        self.log("val/acc_best", self.val_acc_best.compute(), prog_bar=True)

        # 计算当前验证周期的SSIM
        ssim = self.ssim.compute()  # 获取当前验证周期的SSIM
        # 更新最佳SSIM
        self.ssim_best(ssim)  # 更新迄今为止的最佳SSIM
        # 记录最佳SSIM到日志
        self.log("val/ssim_best", self.ssim_best.compute(), prog_bar=True)
        
    def on_validation_epoch_end(self) -> None:
        """
        Lightning钩子函数，在验证周期结束时调用。
        此函数通常用于执行验证周期结束后的清理工作或额外计算。
        当前实现为空，可根据需要添加具体逻辑。
        """
        pass

    def test_step(self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> None:
        """
        执行单步测试，计算并记录测试集的损失和指标
        
        参数:
            batch (Tuple[torch.Tensor, torch.Tensor]): 包含输入图像和目标标签的数据批次
            batch_idx (int): 当前批次的索引
        """
        # 调用共享步骤，获取损失、预测图像和目标图像
        loss, preds, targets = self.shared_step(batch, "test")
        
        # 计算测试集的RMSE（均方根误差）
        test_rmse = self.mult.to(self.device, dtype=preds.dtype) * weighted_rmse_torch(preds, targets)
        test_rmse = test_rmse.detach()
        
        # 计算测试集的ACC（准确率）
        test_acc = weighted_acc_torch(preds - self.clims[2].to(self.device, dtype=preds.dtype), 
                                    targets - self.clims[2].to(self.device, dtype=preds.dtype))
        test_acc = test_acc.detach()

        # 更新并记录测试集的损失和指标
        self.test_loss(loss)
        self.log("test/loss", self.test_loss, on_step=False, on_epoch=True, prog_bar=True)
        self.log("test/rmse", test_rmse, on_step=False, on_epoch=True, prog_bar=True)
        self.log("test/acc", test_acc, on_step=False, on_epoch=True, prog_bar=True)

    def on_test_epoch_end(self) -> None:
        """
        Lightning钩子函数，在测试周期结束时调用。
        此函数通常用于执行测试周期结束后的清理工作或额外计算。
        当前实现为空，可根据需要添加具体逻辑。
        """
        pass

    def configure_optimizers(self):
        """
        配置优化器和学习率调度器。
        在GAN模型中，通常需要为生成器和判别器分别配置优化器。
        
        返回:
            list: 包含优化器的列表
            list: 包含学习率调度器的列表
        """
        # 配置生成器的优化器
        g_opt = self.hparams.g_optimizer(params=self.g_params)
        # 配置生成器的后续学习率调度器
        g_after_scheduler = self.hparams.after_scheduler(optimizer=g_opt)
        # 配置生成器的学习率调度器
        g_sch = self.hparams.scheduler(optimizer=g_opt, after_scheduler=g_after_scheduler)

        # 配置判别器A的优化器
        d_A_opt = self.hparams.d_optimizer(params=self.d_A_params)
        # 配置判别器A的后续学习率调度器
        d_A_after_scheduler = self.hparams.after_scheduler(optimizer=d_A_opt)
        # 配置判别器A的学习率调度器
        d_A_sch = self.hparams.scheduler(optimizer=d_A_opt, after_scheduler=d_A_after_scheduler)

        # 配置判别器B的优化器
        d_B_opt = self.hparams.d_optimizer(params=self.d_B_params)
        # 配置判别器B的后续学习率调度器
        d_B_after_scheduler = self.hparams.after_scheduler(optimizer=d_B_opt)
        # 配置判别器B的学习率调度器
        d_B_sch = self.hparams.scheduler(optimizer=d_B_opt, after_scheduler=d_B_after_scheduler)

        # 返回优化器和学习率调度器的列表
        return [g_opt, d_A_opt, d_B_opt], [g_sch, d_A_sch, d_B_sch]


if __name__ == "__main__":
    _ = AssimilateLitModule(None, None, None, None)
