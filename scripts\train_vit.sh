#!/bin/bash

# SLURM SUBMIT SCRIPT
#SBATCH --nodes=1
#SBATCH -p A100
#SBATCH --ntasks-per-node=3
#SBATCH --gres=gpu:3
#SBATCH --nodelist=gpunode58
#SBATCH --cpus-per-task=4
#SBATCH --hint=nomultithread
#SBATCH --qos=qos_gpu-t3
#SBATCH --mem=500G
#SBATCH --output=./slurmlogs/train-vit-%j.out
#SBATCH --error=./slurmlogs/train-vit-%j.err

# Schedule execution of many runs
# Run from root folder with: bash scripts/schedule.sh

srun python src/train.py trainer=ddp trainer.num_nodes=1 trainer.devices=3 datamodule.batch_size=48 model=vit task_name=vit