#!/bin/bash

# SLURM SUBMIT SCRIPT
#SBATCH --nodes=1
#SBATCH -p A100
#SBATCH --ntasks-per-node=4
#SBATCH --gres=gpu:4
#SBATCH --nodelist=gpunode55
#SBATCH --cpus-per-task=4
#SBATCH --hint=nomultithread
#SBATCH --qos=qos_gpu-t3
#SBATCH --mem=200G
#SBATCH --output=./slurmlogs/train-afnonet-%j.out
#SBATCH --error=./slurmlogs/train-afnonet-%j.err

# Schedule execution of many runs
# Run from root folder with: bash scripts/schedule.sh

srun python src/train.py trainer=ddp trainer.num_nodes=1 trainer.devices=null datamodule.batch_size=64 model=afnonet task_name=afnonet