"""
CycleGAN模型在Z500（500百帕地转位势高度场）数据同化任务上的训练脚本。

这个脚本执行以下操作：
1. 列出系统中的某些目录内容，用于调试和环境检查。
2. 使用指定的配置运行模型训练过程。
3. 在训练完成后打印确认信息。

使用方法：
    直接运行此脚本，无需额外参数。脚本会使用预定义的配置执行训练过程。

依赖：
    - Python 3.x
    - 项目特定的训练脚本（位于 /mnt/c/Users/<USER>/Documents/同步盘/project/自科基金/研发相关/4DVarGAN_V2/src/train.py）
    - 相关的数据和模型配置文件

注意：
    - 确保在运行此脚本之前，所有必要的数据和配置文件都已就位。
    - 此脚本假设在特定的环境中运行，可能需要根据实际部署环境进行调整。
"""

import os  # 导入os模块，用于执行系统命令

# 使用os.system()执行一系列shell命令
os.system(""" 
    # 列出 /home 目录的内容
    ls /home && 
    
    echo "show code" && 
    # 列出 /tmp/code 目录的内容
    ls /tmp/code && 
    
    echo "show dataset" && 
    # 列出 /tmp/dataset 目录的内容
    ls /tmp/dataset && 
    
    echo "show output" && 
    # 列出 /tmp/output 目录的内容
    ls /tmp/output  && 
    
    # 运行训练脚本，使用指定的配置参数
    python /mnt/c/Users/<USER>/Documents/同步盘/project/自科基金/研发相关/4DVarGAN_V2/src/train.py \
    model=cyclegan \
    paths=assim_local_z500 \
    datamodule=ncassimilate_z500 \
    datamodule.batch_size=32 \
    trainer.max_epochs=50 \
    test=True  && 
    
    # 打印训练完成信息
    echo "train done" 
""")