{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/path/to/custom/memory.json"}}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/path/to/other/allowed/dir"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}