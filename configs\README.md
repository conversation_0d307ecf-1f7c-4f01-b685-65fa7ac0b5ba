# 配置文件

本目录包含 4DVarGAN 项目的配置文件。

## 目录结构

```
configs/
├── callbacks/     # 回调函数配置
├── datamodule/    # 数据模块配置
├── debug/         # 调试配置
├── experiment/    # 实验配置
├── extras/        # 额外配置
├── hparams_search/# 超参数搜索配置
├── hydra/         # Hydra配置
├── local/         # 本地配置
├── logger/        # 日志配置
├── model/         # 模型配置
├── paths/         # 路径配置
├── trainer/       # 训练器配置
├── eval.yaml      # 评估配置
└── train.yaml     # 训练配置
```

## 关键文件

- `train.yaml`: 主要的训练配置文件。
- `eval.yaml`: 评估配置文件。

## 使用方法

这些配置文件与Hydra框架一起使用，Hydra是一个用于优雅配置复杂应用程序的框架。要使用特定配置，可以运行：

```bash
python train.py --config-name=my_config
```

或者覆盖特定参数：

```bash
python train.py datamodule.batch_size=64 model.lr=0.01
```

## 添加新配置

要添加新配置：

1. 在适当的子目录中创建新的YAML文件。
2. 遵循现有的格式和结构。
3. 如有必要，在主要的`train.yaml`或`eval.yaml`中引用新配置。

## 最佳实践

- 保持配置模块化和可重用。
- 使用继承来避免重复。
- 对任何不明显的配置选项进行文档说明。
- 将配置与代码一起进行版本控制。

有关使用这些配置的更多详细信息，请参阅主项目文档。