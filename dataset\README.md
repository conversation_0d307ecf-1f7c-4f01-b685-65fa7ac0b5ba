# 数据集

本目录包含4DVarGAN项目使用的数据集。

## 目录结构

```
dataset/
├── background/    # 背景场数据
│   └── geopotential_500_5.625deg/
├── ckpt_z500/    # 模型检查点
├── era5/         # ERA5再分析数据
│   └── geopotential_500_5.625deg/
├── obs/          # 观测数据
│   └── geopotential_500_5.625deg/
└── obs_mask/     # 观测掩码
```

## 数据说明

### 背景场数据
- 位置：`background/geopotential_500_5.625deg/`
- 内容：500hPa位势高度的背景场数据
- 分辨率：5.625度
- 年份：2010-2018
- 数据分割：训练集(2010-2015)，验证集(2016)，测试集(2017-2018)

### ERA5再分析数据
- 位置：`era5/geopotential_500_5.625deg/`
- 内容：500hPa位势高度的ERA5再分析数据
- 分辨率：5.625度
- 附加文件：
  - `normalize_mean.npy`：标准化均值
  - `normalize_std.npy`：标准化标准差
  - `climatology.npy`：气候态数据

### 观测数据
- 位置：`obs/geopotential_500_5.625deg/`
- 内容：500hPa位势高度的观测数据
- 分辨率：5.625度
- 年份：2010-2018

### 观测掩码
- 位置：`obs_mask/`
- 内容：部分观测的掩码
- 覆盖率：5%、10%、15%、20%
- 格式：NetCDF文件

## 数据格式

### NetCDF文件
- 维度：时间、纬度、经度
- 变量：geopotential_500hPa（位势高度）
- 单位：m²/s²

### 模型检查点
- 位置：`ckpt_z500/`
- 格式：PyTorch检查点文件(.ckpt)
- 模型：
  - 4DVarCycleGAN（有无尺度版本）
  - 4DVarGAN（有无尺度版本）
  - 4DVarNet

## 数据处理

数据预处理脚本和工具，请参考：
- `src/data_factory/calculate_scaler.py`
- `src/data_factory/generate_background_nc.py`
- `src/data_factory/generate_obs_mask_nc.py`
- `src/data_factory/generate_obs_nc.py`

## 使用方法

```python
# 加载数据示例
from src.datamodules.assimilate import NCDataModule

datamodule = NCDataModule(
    data_dir="dataset/era5/geopotential_500_5.625deg",
    batch_size=32
)
```

## 数据政策

- ERA5数据受Copernicus数据政策约束
- 使用数据时请引用适当的来源
- 某些数据可能需要特定的许可或权限

## 最佳实践

1. 始终使用提供的数据加载器
2. 验证数据标准化
3. 检查掩码一致性
4. 监控数据质量

有关数据及其使用的更多信息，请参阅主项目文档。