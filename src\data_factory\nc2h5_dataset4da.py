# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
import torch
from src.utils.data_utils import DEFAULT_PRESSURE_LEVELS, NAME_TO_VAR
from src.models.forecast.forecast_module import ForecastLitModule

HOURS_PER_YEAR = 8760  # 365-day year

VARIABLES = [
  "geopotential_500",
  "temperature_850"  
] 

def nc2np(path,
          xb_dir,
          obs_dir,
          variables,
          resolution,
          years,
          save_dir,
          partition,
          lead_time,
          mask,
          num_shards_per_year):
    os.makedirs(os.path.join(save_dir, partition), exist_ok=True)

    for year in tqdm(years):
        np_vars = {}
        np_obs_var = {}

        xb = xr.open_mfdataset(os.path.join(xb_dir, f"*{year}.nc"), combine="by_coords", parallel=True)
        time = xb["time"]

        # non-constant fields
        for var in variables:
            if var == "geopotential_500":
                ps = glob.glob(os.path.join(path, "z500", f"*{year}*.nc"))
            elif var == "temperature_850":
                ps = glob.glob(os.path.join(path, "t850", f"*{year}*.nc"))
            ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True).sel(time=time)  # dataset for a single variable
            code = NAME_TO_VAR[var]

            if len(ds[code].shape) == 3:  # surface level variables
                ds[code] = ds[code].expand_dims("val", axis=1)
                # remove the last 24 hours if this year has 366 days
                np_vars[var] = ds[code].to_numpy()

            else:  # multiple-level variables, only use a subset
                assert len(ds[code].shape) == 4
                all_levels = ds["level"][:].to_numpy()
                all_levels = np.intersect1d(all_levels, DEFAULT_PRESSURE_LEVELS)
                for level in all_levels:
                    ds_level = ds.sel(level=[level])
                    level = int(level)
                    # remove the last 24 hours if this year has 366 days
                    np_vars[f"{var}_{level}"] = ds_level[code].to_numpy()


        keys_ = list(np_vars.keys())
        np_all = np.concatenate([np_vars[key_] for key_ in keys_], axis=1)
        np_obs = np.concatenate([np_obs_var[key_] for key_ in keys_], axis=2)

        assert HOURS_PER_YEAR % (12 * num_shards_per_year) == 0
        num_samples_per_shard = HOURS_PER_YEAR // (12 * num_shards_per_year)
        for shard_id in range(num_shards_per_year):
            start_id = shard_id * num_samples_per_shard
            end_id = start_id + num_samples_per_shard
            fdest = h5py.File(os.path.join(save_dir, partition, f"{year}_{shard_id}.h5"), 'w')
            fdest.create_dataset("xb", data=xb["background"].to_numpy()[start_id:end_id])
            fdest.create_dataset("obs", data=np_obs[start_id:end_id])
            fdest.create_dataset("mask", data=np.repeat(mask, num_samples_per_shard))
            fdest.create_dataset("xt", data=np_all[start_id:end_id])
            fdest.close()

@click.command()
@click.option("--root_dir", type=click.Path(exists=True))
@click.option("--xb_dir", type=click.Path(exists=True))
@click.option("--obs_dir", type=click.Path(exists=True))
@click.option("--obs_info_dir", type=click.Path(exists=True))
@click.option("--save_dir", type=str)
@click.option(
    "--variables",
    "-v",
    type=click.STRING,
    multiple=True,
    default=[
        "geopotential_500",
        "temperature_850",
    ],
)
@click.option(
    "--obs_variables",
    "-ov",
    type=click.STRING,
    multiple=True,
    default=[
        "geopotential_500",
        "temperature_850",
    ],
)
@click.option("--resolution", type=float, default=2.8125)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
@click.option("--lead_time", type=int, default=24)
@click.option("--num_shards", type=int, default=1)
def main(
    root_dir,
    xb_dir,
    obs_dir,
    obs_info_dir,
    save_dir,
    variables,
    resolution,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
    lead_time,
    num_shards,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)

    sf_mask = np.load(f"{obs_info_dir}/surface/station_locations.npy")
    up_mask = np.load(f"{obs_info_dir}/upper-air/station_locations.npy")

    mask = np.zeros((len(variables), sf_mask.shape[0], sf_mask.shape[1]))
    mask[0] = sf_mask
    for i in range(4, len(variables)):
        mask[i] = up_mask

    nc2np(root_dir, xb_dir, obs_dir, variables, resolution, train_years, save_dir, "train", lead_time, mask, num_shards)
    nc2np(root_dir, xb_dir, obs_dir, variables, resolution, val_years, save_dir, "val", lead_time, mask, num_shards)
    nc2np(root_dir, xb_dir, obs_dir, variables, resolution, test_years, save_dir, "test", lead_time, mask, num_shards)

if __name__ == "__main__":
    main()
