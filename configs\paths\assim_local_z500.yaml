# path to root directory
# this requires PROJECT_ROOT environment variable to exist
# PROJECT_ROOT is inferred and set by pyrootutils package in `train.py` and `eval.py`
root_dir: ${oc.env:PROJECT_ROOT}

# path to data directory
era5_dir: ${paths.root_dir}/dataset/era5/geopotential_500_5.625deg
background_dir: ${paths.root_dir}/dataset/background/geopotential_500_5.625deg
obs_dir: ${paths.root_dir}/dataset/obs/geopotential_500_5.625deg
obs_mask_dir: ${paths.root_dir}/dataset/obs_mask
#ckpt_dir: ${paths.root_dir}/dataset/ckpt

# path to logging directory
log_dir: ${paths.root_dir}/logs/

# path to output directory, created dynamically by hydra
# path generation pattern is specified in `configs/hydra/default.yaml`
# use it to store all files generated during the run, like ckpts and metrics
output_dir: ${hydra:runtime.output_dir}

# path to working directory
work_dir: ${hydra:runtime.cwd}