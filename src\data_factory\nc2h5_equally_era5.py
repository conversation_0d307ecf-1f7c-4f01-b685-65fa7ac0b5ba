# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import glob
import os
import sys
sys.path.append(".")
import click
import numpy as np
import xarray as xr
from tqdm import tqdm
import h5py
from src.utils.data_utils import NAME_TO_VAR

HOURS_PER_YEAR = 8760  # 365-day year

def nc2np(path, variables, years, save_dir, partition, resolution, num_shards_per_year):
    os.makedirs(os.path.join(save_dir, partition), exist_ok=True)

    if partition == "train":
        normalize_mean = {}
        normalize_std = {}
    climatology = {}

    for year in tqdm(years):
        np_vars = {}

        # non-constant fields
        for var in variables:
            ps = glob.glob(os.path.join(f"{path}", f"{var}_{resolution}deg", f"*{year}*.nc"))
            ds = xr.open_mfdataset(ps, combine="by_coords", parallel=True)  # dataset for a single variable
            code = NAME_TO_VAR[var]
            
            # remove the last 24 hours if this year has 366 days
            np_vars[f"{var}"] = ds[code].to_numpy()[:HOURS_PER_YEAR:6]

            if partition == "train":  # compute mean and std of each var in each year
                var_mean_yearly = np_vars[f"{var}"].mean(axis=(0, 1, 2))
                var_std_yearly = np_vars[f"{var}"].std(axis=(0, 1, 2))
                if var not in normalize_mean:
                    normalize_mean[f"{var}"] = [var_mean_yearly]
                    normalize_std[f"{var}"] = [var_std_yearly]
                else:
                    normalize_mean[f"{var}"].append(var_mean_yearly)
                    normalize_std[f"{var}"].append(var_std_yearly)

            clim_yearly = np_vars[f"{var}"].mean(axis=0)
            if f"{var}" not in climatology:
                climatology[f"{var}"] = [clim_yearly]
            else:
                climatology[f"{var}"].append(clim_yearly)

        assert HOURS_PER_YEAR % (6 * num_shards_per_year) == 0
        num_hrs_per_shard = HOURS_PER_YEAR // (6 * num_shards_per_year)
        for shard_id in range(num_shards_per_year):
            start_id = shard_id * num_hrs_per_shard
            end_id = start_id + num_hrs_per_shard
            fdest = h5py.File(os.path.join(save_dir, partition, f"{year}_{shard_id}.h5"), 'w')
            for k in np_vars.keys():
                fdest.create_dataset(k, data=np_vars[k][start_id:end_id])
            fdest.close()

    if partition == "train":
        for var in normalize_mean.keys():
            # if var not in constant_fields:
            normalize_mean[var] = np.stack(normalize_mean[var], axis=0)
            normalize_std[var] = np.stack(normalize_std[var], axis=0)

        for var in normalize_mean.keys():  # aggregate over the years
            # if var not in constant_fields:
            mean, std = normalize_mean[var], normalize_std[var]
            # var(X) = E[var(X|Y)] + var(E[X|Y])
            variance = (std**2).mean(axis=0) + (mean**2).mean(axis=0) - mean.mean(axis=0) ** 2
            std = np.sqrt(variance)
            # E[X] = E[E[X|Y]]
            mean = mean.mean(axis=0)
            normalize_mean[var] = mean
            normalize_std[var] = std

        np.savez(os.path.join(save_dir, "normalize_mean.npz"), **normalize_mean)
        np.savez(os.path.join(save_dir, "normalize_std.npz"), **normalize_std)

    for var in climatology.keys():
        climatology[var] = np.stack(climatology[var], axis=0)
    climatology = {k: np.mean(v, axis=0) for k, v in climatology.items()}
    np.savez(
        os.path.join(save_dir, partition, "climatology.npz"),
        **climatology,
    )


@click.command()
@click.option("--root_dir", type=click.Path(exists=True))
@click.option("--save_dir", type=str)
@click.option(
    "--variables",
    "-v",
    type=click.STRING,
    multiple=True,
    default=[
        "geopotential_500",
        "temperature_850",
    ],
)
@click.option("--resolution", type=float, default=5.625)
@click.option("--start_train_year", type=int, default=1979)
@click.option("--start_val_year", type=int, default=2016)
@click.option("--start_test_year", type=int, default=2017)
@click.option("--end_year", type=int, default=2019)
@click.option("--n_shards", type=int, default=1)
def main(
    root_dir,
    save_dir,
    variables,
    resolution,
    start_train_year,
    start_val_year,
    start_test_year,
    end_year,
    n_shards,
):
    assert start_val_year > start_train_year and start_test_year > start_val_year and end_year > start_test_year
    train_years = range(start_train_year, start_val_year)
    val_years = range(start_val_year, start_test_year)
    test_years = range(start_test_year, end_year)

    os.makedirs(save_dir, exist_ok=True)

    nc2np(root_dir, variables, train_years, save_dir, "train", resolution, n_shards)
    nc2np(root_dir, variables, val_years, save_dir, "val", resolution, n_shards)
    nc2np(root_dir, variables, test_years, save_dir, "test", resolution, n_shards)

    # save lat and lon data
    ps = glob.glob(os.path.join(f"{root_dir}", f"{variables[0]}_{resolution}deg", f"*{train_years[0]}*.nc"))
    x = xr.open_mfdataset(ps[0], parallel=True)
    lat = x["lat"].to_numpy()
    lon = x["lon"].to_numpy()
    np.save(os.path.join(save_dir, "lat.npy"), lat)
    np.save(os.path.join(save_dir, "lon.npy"), lon)


if __name__ == "__main__":
    main()

