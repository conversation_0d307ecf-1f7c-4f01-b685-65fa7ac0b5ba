# 源代码

本目录包含4DVarGAN项目的源代码。

## 目录结构

```
src/
├── da_method/              # 数据同化方法
│   ├── __init__.py
│   └── var4d.py            # 4D变分数据同化实现
├── data_factory/           # 数据处理工厂
│   ├── __init__.py
│   ├── calculate_scaler.py
│   ├── generate_background_nc.py
│   ├── generate_obs_mask_nc.py
│   └── generate_obs_nc.py
├── datamodules/            # 数据模块
│   ├── assimilate/         # 同化数据模块
│   │   ├── __init__.py
│   │   ├── ncdatamodule.py
│   │   └── ncdataset.py
│   ├── forecast/           # 预报数据模块
│   │   ├── __init__.py
│   │   ├── ncdatamodule.py
│   │   └── ncdataset.py
│   └── __init__.py
├── evaluate/               # 评估模块
│   ├── __init__.py
│   ├── eval_daloop.py
│   ├── eval_forecast_model.py
│   ├── eval_medium_forecast.py
│   └── inference.py
├── models/                 # 模型实现
│   ├── assimilate/         # 同化模型
│   │   ├── cyclegan/       # CycleGAN实现
│   │   │   ├── __init__.py
│   │   │   ├── genB_unet.py
│   │   │   ├── genDA_unet.py
│   │   │   └── patch_discriminator.py
│   │   ├── __init__.py
│   │   └── cyclegan_assim_module.py
│   ├── forecast/           # 预报模型
│   │   ├── afnonet/        # AFNO网络
│   │   │   ├── __init__.py
│   │   │   └── arch.py
│   │   ├── layers/         # 模型层
│   │   │   ├── __init__.py
│   │   │   ├── fno_layers.py
│   │   │   └── pos_embed.py
│   │   ├── __init__.py
│   │   └── forecast_module.py
│   └── __init__.py
└── utils/                  # 实用工具
    ├── __init__.py
    ├── logger.py           # 日志工具
    └── metrics.py          # 评估指标
```

## 模块说明

### 数据同化方法 (`da_method/`)
实现了4D变分数据同化算法，包括：
- 成本函数计算
- 梯度计算
- 最小化算法

### 数据处理工厂 (`data_factory/`)
处理和准备数据的工具，包括：
- 计算标准化参数
- 生成背景场数据
- 生成观测掩码
- 生成观测数据

### 数据模块 (`datamodules/`)
使用PyTorch Lightning实现的数据加载和处理模块：
- 同化数据模块：处理同化任务的数据
- 预报数据模块：处理预报任务的数据

### 评估模块 (`evaluate/`)
用于评估模型性能的工具：
- 数据同化循环评估
- 预报模型评估
- 中期预报评估
- 推理工具

### 模型 (`models/`)
神经网络模型实现：
- 同化模型：
  - CycleGAN实现
  - 生成器和判别器架构
- 预报模型：
  - AFNO网络架构
  - 自定义层实现

### 实用工具 (`utils/`)
支持功能：
- 日志工具
- 评估指标

## 使用指南

### 导入模块
```python
# 导入数据模块
from src.datamodules.assimilate import NCDataModule

# 导入模型
from src.models.assimilate.cyclegan_assim_module import CycleGANAssimModule

# 导入评估工具
from src.evaluate.eval_daloop import eval_daloop
```

### 数据处理
```python
# 创建数据模块
datamodule = NCDataModule(
    data_dir="path/to/data",
    batch_size=32,
    num_workers=4
)
datamodule.setup()
```

### 模型训练
```python
# 创建模型
model = CycleGANAssimModule(
    lr=0.0002,
    b1=0.5,
    b2=0.999
)

# 使用PyTorch Lightning进行训练
trainer = pl.Trainer(gpus=1, max_epochs=100)
trainer.fit(model, datamodule)
```

## 开发指南

### 添加新模型
1. 在适当的子目录中创建新文件
2. 实现PyTorch Lightning模块
3. 注册到相应的__init__.py文件中

### 添加新数据集
1. 在datamodules目录中创建新的数据集类
2. 实现必要的方法：__init__、__len__、__getitem__
3. 创建相应的DataModule类

### 代码风格
- 遵循PEP 8
- 使用类型注释
- 编写文档字符串
- 保持函数简短，单一职责

## 依赖项

主要依赖项：
- PyTorch
- PyTorch Lightning
- NumPy
- xarray
- netCDF4

有关完整依赖列表，请参阅项目根目录中的requirements.txt文件。