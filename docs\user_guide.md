# 4DVarGAN_V2 用户指南

## 1. 安装与配置

### 1.1 环境要求

#### 1.1.1 硬件要求
- **CPU**: 推荐8核以上
- **内存**: 最低16GB，推荐32GB以上
- **GPU**: NVIDIA GPU，至少8GB显存，推荐16GB以上
- **存储**: 至少100GB可用空间（用于数据集和模型）

#### 1.1.2 软件要求
- **操作系统**: Linux (推荐Ubuntu 20.04或更高版本)，也支持Windows和macOS
- **Python**: 3.8或更高版本
- **CUDA**: 11.3或更高版本(使用GPU时)
- **cuDNN**: 与CUDA版本兼容

### 1.2 安装步骤

#### 1.2.1 创建虚拟环境
```bash
# 使用conda创建虚拟环境
conda create -n 4dvargan python=3.8
conda activate 4dvargan

# 或使用venv
python -m venv 4dvargan_env
source 4dvargan_env/bin/activate  # Linux/macOS
4dvargan_env\Scripts\activate  # Windows
```

#### 1.2.2 安装依赖
```bash
# 克隆仓库
git clone https://github.com/your-username/4DVarGAN_V2.git
cd 4DVarGAN_V2

# 安装依赖
pip install -r requirements.txt

# 安装PyTorch (根据CUDA版本选择合适的命令)
# CUDA 11.3
pip install torch==1.12.1+cu113 torchvision==0.13.1+cu113 -f https://download.pytorch.org/whl/torch_stable.html

# 安装项目包
pip install -e .
```

#### 1.2.3 验证安装
```bash
# 检查环境
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"

# 运行测试
python -m pytest tests/
```

### 1.3 配置设置

#### 1.3.1 环境变量
创建`.env`文件（基于`.env.example`）:
```
# 数据路径配置
DATA_ROOT=/path/to/your/data
OUTPUT_DIR=/path/to/output

# GPU设置
CUDA_VISIBLE_DEVICES=0,1

# 日志设置
LOG_LEVEL=INFO
```

#### 1.3.2 配置文件
主要配置文件位于`configs/`目录，可以根据需要修改。

## 2. 数据准备

### 2.1 数据集结构

#### 2.1.1 支持的数据格式
- **NetCDF**: `.nc`文件，标准气象数据格式
- **HDF5**: `.h5`文件，适用于大型数据集
- **NumPy**: `.npy`文件，用于预处理后的数据

#### 2.1.2 目录结构
```
dataset/
├── raw/                   # 原始数据
│   ├── ERA5/              # ERA5再分析数据
│   └── observations/      # 观测数据
├── processed/             # 预处理后的数据
│   ├── background/        # 背景场
│   ├── observations/      # 处理后的观测
│   └── analysis/          # 参考分析场
└── scalers/               # 标准化参数
```

### 2.2 数据预处理

#### 2.2.1 原始数据下载
```bash
# 下载ERA5数据
python scripts/download_era5.py --year 2018 --month 1 --variable z --level 500

# 或使用提供的shell脚本
bash scripts/download_data.sh
```

#### 2.2.2 数据转换与标准化
```bash
# 将NetCDF转换为NumPy数组
python src/data_factory/nc2np.py --input dataset/raw/ERA5 --output dataset/processed --var geopotential_500

# 计算标准化参数
python src/data_factory/calculate_scaler.py --input dataset/processed --output dataset/scalers
```

#### 2.2.3 生成观测数据
```bash
# 从分析场生成观测
python src/data_factory/generate_obs_nc.py --input dataset/processed/analysis --output dataset/processed/observations --coverage 0.2
```

### 2.3 数据集划分

#### 2.3.1 训练/验证/测试集划分
```bash
# 划分数据集
python scripts/split_dataset.py --input dataset/processed --output dataset/splits --train 0.7 --val 0.15 --test 0.15
```

## 3. 模型训练

### 3.1 训练配置

#### 3.1.1 配置文件说明
训练配置使用YAML格式，位于`configs/`目录：

**基本配置** (`configs/train.yaml`):
```yaml
defaults:
  - model: fdvarcyclegan_unet_all
  - datamodule: ncassimilate_z500
  - trainer: default
  - paths: assim_openi_z500
  - callbacks: default
  - logger: tensorboard
  - _self_

seed: 42
print_config: True
```

**模型配置** (`configs/model/fdvarcyclegan_unet_all.yaml`):
```yaml
_target_: src.models.assimilate.fdvarcyclegan_assim_module.FDVarCycleGANModule

g_A2B:
  _target_: src.models.assimilate.fdvarcyclegan.genB_unet.UNet
  in_channels: 4
  hidden_channels: 32
  out_channels: 1
  num_downsampling: 2
  num_resnet_blocks: 4

g_B2A:
  _target_: src.models.assimilate.fdvarcyclegan.genDA_unet.UNet
  in_channels: 4
  hidden_channels: 32
  out_channels: 1
  num_downsampling: 2
  num_resnet_blocks: 4

d_A:
  _target_: src.models.assimilate.fdvarcyclegan.patch_discriminator.PatchDiscriminator
  in_channels: 1
  hidden_channels: 32
  num_layers: 3

d_B:
  _target_: src.models.assimilate.fdvarcyclegan.patch_discriminator.PatchDiscriminator
  in_channels: 1
  hidden_channels: 32
  num_layers: 3

lambda_A: 10.0
lambda_B: 10.0
lambda_idt: 5.0
lambda_pred: 10.0

g_optimizer:
  _target_: torch.optim.AdamW
  lr: 5e-3
  betas: [0.5, 0.999]

d_optimizer:
  _target_: torch.optim.AdamW
  lr: 2e-3
  betas: [0.5, 0.999]

scheduler:
  _target_: src.utils.train_utils.GradualWarmupScheduler
  multiplier: 1
  total_epoch: 5

after_scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  T_max: 45
  eta_min: 1e-8

pool_size: 50
```

#### 3.1.2 超参数设置
主要超参数包括：
- **学习率**: 生成器5e-3，判别器2e-3
- **批量大小**: 通常设置为16或32
- **训练轮数**: 通常为50轮
- **损失权重**: lambda_A=10.0, lambda_B=10.0, lambda_idt=5.0, lambda_pred=10.0

### 3.2 训练命令

#### 3.2.1 基本训练
```bash
# 使用默认配置训练
python train.py

# 指定配置文件
python train.py --config-name=custom_config

# 覆盖配置参数
python train.py model=fdvarcyclegan_unet_all trainer.max_epochs=100
```

#### 3.2.2 分布式训练
```bash
# 单机多卡训练
python train.py trainer=ddp trainer.gpus=4

# 多节点训练
python train.py trainer=ddp trainer.num_nodes=2 trainer.gpus=4
```

#### 3.2.3 恢复训练
```bash
# 从检查点恢复
python train.py trainer.resume_from_checkpoint=path/to/checkpoint.ckpt
```

### 3.3 训练监控

#### 3.3.1 TensorBoard监控
```bash
# 启动TensorBoard
tensorboard --logdir logs/tensorboard

# 在浏览器中访问
# http://localhost:6006
```

#### 3.3.2 进度与指标
TensorBoard中可以监控的指标：
- **损失**: 生成器损失、判别器损失、循环一致性损失等
- **评估指标**: RMSE、ACC等
- **学习率**: 学习率变化曲线
- **梯度**: 梯度范数统计
- **可视化**: 生成样本、真实样本对比

## 4. 模型评估

### 4.1 评估配置

#### 4.1.1 评估参数
评估配置文件(`configs/eval.yaml`):
```yaml
defaults:
  - model: fdvarcyclegan_unet_all
  - datamodule: ncassimilate_z500
  - paths: assim_openi_z500
  - _self_

checkpoint_path: ${paths.log_dir}/checkpoints/last.ckpt
output_dir: ${paths.log_dir}/evaluation

metrics:
  - rmse
  - acc
  - bias
  - energy_spectrum

visualization:
  enabled: true
  num_samples: 10
  save_format: png
```

### 4.2 评估命令

#### 4.2.1 基本评估
```bash
# 使用默认配置评估
python eval.py

# 指定配置和检查点
python eval.py checkpoint_path=/path/to/checkpoint.ckpt
```

#### 4.2.2 数据同化循环评估
```bash
# 评估数据同化循环
python scripts/eval_daloop_z500_obspartial0.2.py --checkpoint /path/to/checkpoint.ckpt --cycles 10
```

### 4.3 结果分析

#### 4.3.1 指标计算
评估脚本会计算多种指标：
- **RMSE**: 均方根误差
- **ACC**: 异常相关系数
- **BIAS**: 系统偏差
- **能量谱**: 不同尺度的能量分布

#### 4.3.2 可视化结果
生成的可视化包括：
- **空间分布图**: 分析场、背景场、观测场的对比
- **误差分布图**: 分析场与真值的误差分布
- **时间演变**: 多个同化循环的结果演变
- **能量谱分析**: 不同波数的能量谱对比

## 5. 推理与应用

### 5.1 模型部署

#### 5.1.1 导出模型
```bash
# 导出为TorchScript格式
python scripts/export_model.py --checkpoint /path/to/checkpoint.ckpt --output /path/to/model.pt --format torchscript

# 导出为ONNX格式
python scripts/export_model.py --checkpoint /path/to/checkpoint.ckpt --output /path/to/model.onnx --format onnx
```

#### 5.1.2 推理服务
```bash
# 启动推理服务
python scripts/inference_server.py --model /path/to/model.pt --port 8000
```

### 5.2 批量推理

#### 5.2.1 批处理命令
```bash
# 批量处理数据
python scripts/batch_inference.py --model /path/to/model.pt --input /path/to/input/dir --output /path/to/output/dir
```

#### 5.2.2 结果保存
```bash
# 将结果保存为NetCDF格式
python scripts/save_results.py --input /path/to/results --output /path/to/output.nc --format netcdf
```

### 5.3 集成到数值预报系统

#### 5.3.1 API接口
```python
# Python API示例
from fdvargan import FDVarGANPredictor

# 初始化预测器
predictor = FDVarGANPredictor(model_path="/path/to/model.pt")

# 进行预测
analysis = predictor.predict(background=background_field, observations=obs_field)
```

#### 5.3.2 配置文件
集成配置示例(`integration_config.yaml`):
```yaml
model:
  path: /path/to/model.pt
  device: cuda:0
  batch_size: 16

preprocessing:
  scaler_path: /path/to/scaler.pkl
  normalize: true

postprocessing:
  denormalize: true
  clip_values: [-3.0, 3.0]

logging:
  level: INFO
  file: /path/to/logs/integration.log
```

## 6. 高级功能

### 6.1 超参数优化

#### 6.1.1 使用Optuna
```bash
# 运行超参数搜索
python scripts/hparam_search.py --config configs/hparams_search/optuna.yaml --trials 50
```

#### 6.1.2 优化配置
超参数搜索配置(`configs/hparams_search/optuna.yaml`):
```yaml
defaults:
  - override /hydra/sweeper: optuna

hydra:
  sweeper:
    sampler:
      _target_: optuna.samplers.TPESampler
      seed: 123
    direction: minimize
    study_name: 4dvargan_optimization
    storage: sqlite:///optuna.db
    n_trials: 50
    n_jobs: 1
    params:
      model.g_optimizer.lr: interval(1e-4, 1e-2)
      model.d_optimizer.lr: interval(1e-4, 1e-2)
      model.lambda_A: interval(5.0, 20.0)
      model.lambda_B: interval(5.0, 20.0)
      model.lambda_idt: interval(0.0, 10.0)
```

### 6.2 自定义模型

#### 6.2.1 创建新模型
自定义模型需要继承基类并实现必要方法：

```python
# src/models/custom_model.py
import torch
import torch.nn as nn
import pytorch_lightning as pl

class CustomGenerator(nn.Module):
    def __init__(self, in_channels, out_channels, hidden_dim=64):
        super().__init__()
        # 定义网络结构
        
    def forward(self, x):
        # 实现前向传播
        return output

class CustomAssimilationModule(pl.LightningModule):
    def __init__(self, g_model, d_model, **kwargs):
        super().__init__()
        self.save_hyperparameters()
        
        # 初始化模型组件
        self.generator = g_model
        self.discriminator = d_model
        
    def forward(self, x):
        return self.generator(x)
        
    def training_step(self, batch, batch_idx, optimizer_idx):
        # 实现训练步骤
        
    def configure_optimizers(self):
        # 配置优化器
```

#### 6.2.2 注册新模型
创建配置文件(`configs/model/custom_model.yaml`):
```yaml
_target_: src.models.custom_model.CustomAssimilationModule

g_model:
  _target_: src.models.custom_model.CustomGenerator
  in_channels: 4
  out_channels: 1
  hidden_dim: 64

d_model:
  _target_: src.models.assimilate.discriminator.PatchDiscriminator
  in_channels: 1
  hidden_channels: 32
  num_layers: 3

# 其他参数
```

### 6.3 自定义损失函数

#### 6.3.1 实现新损失
```python
# src/models/losses.py
import torch
import torch.nn as nn

class PhysicsInformedLoss(nn.Module):
    def __init__(self, weight=1.0):
        super().__init__()
        self.weight = weight
        
    def forward(self, pred, target, gradient=None):
        # 基本重建损失
        recon_loss = torch.nn.functional.mse_loss(pred, target)
        
        # 物理约束损失
        if gradient is not None:
            # 计算梯度一致性
            pred_grad = torch.autograd.grad(
                pred.sum(), gradient, create_graph=True)[0]
            target_grad = torch.autograd.grad(
                target.sum(), gradient, create_graph=True)[0]
            physics_loss = torch.nn.functional.mse_loss(pred_grad, target_grad)
        else:
            physics_loss = 0
            
        return recon_loss + self.weight * physics_loss
```

#### 6.3.2 在模型中使用
```python
# 在模型中使用自定义损失
from src.models.losses import PhysicsInformedLoss

class CustomModule(pl.LightningModule):
    def __init__(self):
        super().__init__()
        # 初始化损失函数
        self.physics_loss = PhysicsInformedLoss(weight=0.5)
        
    def training_step(self, batch, batch_idx):
        # 使用损失函数
        loss = self.physics_loss(pred, target, gradient=input)
        return loss
```

## 7. 故障排除

### 7.1 常见问题

#### 7.1.1 内存错误
**问题**: CUDA out of memory
**解决方案**:
- 减小批量大小
- 使用梯度累积
- 启用混合精度训练
```bash
python train.py trainer.gpus=1 datamodule.batch_size=8 trainer.accumulate_grad_batches=4 trainer.precision=16
```

#### 7.1.2 训练不稳定
**问题**: 生成器或判别器损失异常
**解决方案**:
- 调整学习率
- 使用梯度裁剪
- 调整损失权重
```bash
python train.py model.g_optimizer.lr=1e-4 model.d_optimizer.lr=5e-5 trainer.gradient_clip_val=0.5
```

#### 7.1.3 结果质量差
**问题**: 生成的分析场质量不佳
**解决方案**:
- 检查数据预处理
- 增加物理约束权重
- 尝试不同的网络架构
```bash
python train.py model=fdvarunet model.lambda_physics=2.0
```

### 7.2 调试技巧

#### 7.2.1 日志分析
```bash
# 设置详细日志
python train.py logger.level=DEBUG

# 分析日志文件
tail -f logs/train.log | grep ERROR
```

#### 7.2.2 模型检查
```bash
# 打印模型结构
python scripts/print_model.py --checkpoint /path/to/checkpoint.ckpt

# 检查梯度
python scripts/check_gradients.py --config configs/model/fdvarcyclegan_unet_all.yaml
```

#### 7.2.3 可视化调试
```bash
# 生成中间结果可视化
python scripts/visualize_features.py --checkpoint /path/to/checkpoint.ckpt --layer encoder.3
```

## 8. 最佳实践

### 8.1 训练策略

#### 8.1.1 学习率调度
- 使用预热阶段(5轮)
- 之后使用余弦退火
- 设置适当的最小学习率(1e-8)

#### 8.1.2 正则化技术
- 权重衰减: 1e-4
- Dropout: 0.1 (仅在判别器中使用)
- 批量归一化: 在生成器中的每个卷积层后使用

#### 8.1.3 训练技巧
- 使用混合精度训练加速
- 实现早停机制(patience=10)
- 保存最佳模型(基于验证集RMSE)

### 8.2 模型选择

#### 8.2.1 适用场景
- **FDVarGAN**: 适用于观测稀疏的情况
- **FDVarCycleGAN**: 适用于需要双向映射的情况
- **FDVarUNet**: 计算资源有限时的轻量级选择

#### 8.2.2 参数设置
- 小数据集: 减小网络深度，增加正则化
- 大数据集: 增加网络容量，减少正则化
- 复杂物理过程: 增加ResNet块数量

### 8.3 性能优化

#### 8.3.1 计算优化
- 使用混合精度训练
- 启用CUDNN基准测试
- 优化数据加载(num_workers=4, pin_memory=True)

#### 8.3.2 内存优化
- 使用梯度检查点
- 实现梯度累积
- 优化批量大小

## 9. 附录

### 9.1 命令行参考

#### 9.1.1 完整命令列表
```
train.py                  # 训练模型
eval.py                   # 评估模型
scripts/
  download_era5.py        # 下载ERA5数据
  nc2np.py                # NetCDF转NumPy
  generate_obs_nc.py      # 生成观测数据
  eval_daloop_*.py        # 评估数据同化循环
  export_model.py         # 导出模型
  batch_inference.py      # 批量推理
  hparam_search.py        # 超参数搜索
  visualize_features.py   # 可视化特征
```

### 9.2 配置文件参考

#### 9.2.1 完整配置结构
```
configs/
├── callbacks/            # 回调函数配置
├── datamodule/           # 数据模块配置
├── debug/                # 调试配置
├── experiment/           # 实验配置
├── hparams_search/       # 超参数搜索配置
├── logger/               # 日志配置
├── model/                # 模型配置
├── paths/                # 路径配置
├── trainer/              # 训练器配置
├── eval.yaml             # 评估配置
└── train.yaml            # 训练配置
```

### 9.3 API参考

#### 9.3.1 核心类
```python
# 模型类
FDVarGANModule            # 基本4DVarGAN模型
FDVarCycleGANModule       # 循环一致性4DVarGAN模型
FDVarUNetModule           # U-Net架构的4DVarGAN模型

# 数据类
NCAssimilateDataModule    # NetCDF数据同化数据模块
NCForecastDataModule      # NetCDF预报数据模块

# 工具类
ModelExporter             # 模型导出工具
InferenceEngine           # 推理引擎
Visualizer                # 可视化工具
```