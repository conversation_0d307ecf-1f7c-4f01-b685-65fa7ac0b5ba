# 4DVarGAN_V2

4DVarGAN_V2是一个基于深度学习的数据同化系统，结合了传统的4D变分方法和生成对抗网络（GAN）的优势。该项目旨在提高气象预报的准确性和效率。

## 项目结构

```
4DVarGAN_V2/
├── configs/        # 配置文件
├── dataset/        # 数据集
├── docs/           # 文档
├── notebooks/      # Jupyter笔记本
├── scripts/        # 实用脚本
└── src/            # 源代码
```

## 主要特性

- 结合4D变分和GAN的创新数据同化方法
- 支持多种观测覆盖率（5%、10%、15%、20%）
- 灵活的模型架构，包括4DVarGAN和4DVarCycleGAN
- 全面的评估和分析工具
- 基于PyTorch Lightning的高效实现

## 快速开始

1. 克隆仓库：
   ```
   git clone https://github.com/your-username/4DVarGAN_V2.git
   cd 4DVarGAN_V2
   ```

2. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

3. 准备数据：
   参考 `dataset/README.md` 获取详细说明。

4. 训练模型：
   ```
   python train.py --config-name=default
   ```

5. 评估模型：
   ```
   python scripts/eval_daloop_z500_obspartial0.2.py
   ```

## 文档

详细的文档可以在 `docs/` 目录中找到：
- 技术文档：`docs/technical_documentation.md`
- 各模块说明：查看每个目录下的README文件

## 数据集

本项目使用的数据集包括：
- ERA5再分析数据
- 生成的观测数据
- 背景场数据

详细信息请参考 `dataset/README.md`。

## 模型

主要模型实现包括：
- 4DVarGAN
- 4DVarCycleGAN
- AFNO网络（用于天气预报）

模型代码位于 `src/models/` 目录。

## 实验和分析

Jupyter笔记本用于实验和结果分析，位于 `notebooks/` 目录。主要包括：
- 消融研究
- 性能分析
- 可视化

## 脚本

实用脚本位于 `scripts/` 目录，用于模型评估和数据处理。

## 配置

使用Hydra进行配置管理。配置文件位于 `configs/` 目录。

## 贡献

欢迎贡献！请查看 `CONTRIBUTING.md`（如果有）了解如何参与项目开发。

## 许可证

本项目采用 [许可证名称] 许可证。详情请见 `LICENSE` 文件。

## 联系方式

如有任何问题或建议，请联系 [您的联系信息]。

## 致谢

感谢所有为本项目做出贡献的研究人员和开发者。特别感谢 [特别致谢对象]。